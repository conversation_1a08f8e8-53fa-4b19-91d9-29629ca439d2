package com.leway.modules.sales.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.leway.api.YshopException;
import com.leway.common.service.impl.BaseServiceImpl;
import com.leway.common.utils.QueryHelpPlus;
import com.leway.constant.ShopConstants;
import com.leway.domain.PageResult;
import com.leway.dozer.service.IGenerator;
import com.leway.enums.AfterSalesStateEnum;
import com.leway.enums.OrderInfoEnum;
import com.leway.enums.OrderRefundStatusEnum;
import com.leway.enums.ShopCommonEnum;
import com.leway.exception.ErrorRequestException;
import com.leway.modules.cart.vo.YxStoreCartQueryVo;
import com.leway.modules.order.domain.Order;
import com.leway.modules.order.domain.OrderCartItem;
import com.leway.modules.order.service.mapper.OrderCartItemMapper;
import com.leway.modules.order.service.mapper.OrderMapper;
import com.leway.modules.product.domain.Product;
import com.leway.modules.product.service.ProductService;
import com.leway.modules.sales.domain.AfterSales;
import com.leway.modules.sales.domain.AfterSalesItem;
import com.leway.modules.sales.domain.AfterSalesStatus;
import com.leway.modules.sales.param.AfterSalesDto;
import com.leway.modules.sales.param.AfterSalesQueryCriteria;
import com.leway.modules.sales.param.SkuParam;
import com.leway.modules.sales.param.StoreAfterSalesParam;
import com.leway.modules.sales.service.AfterSalesService;
import com.leway.modules.sales.service.mapper.StoreAfterSalesItemMapper;
import com.leway.modules.sales.service.mapper.StoreAfterSalesMapper;
import com.leway.modules.sales.service.mapper.StoreAfterSalesStatusMapper;
import com.leway.modules.sales.service.vo.AfterSalesVo;
import com.leway.modules.sales.service.vo.OrderCartInfoVo;
import com.leway.modules.tools.express.KDBirdExpressService;
import com.leway.utils.FileUtil;
import com.leway.utils.RedisUtils;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> le 2021/6/27 15:56
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class AfterSalesServiceImpl extends BaseServiceImpl<StoreAfterSalesMapper, AfterSales>
        implements AfterSalesService {

    /**
     * 订单mapper
     */
    private final OrderMapper storeOrderMapper;

    private final OrderCartItemMapper storeOrderCartInfoMapper;

    private final StoreAfterSalesItemMapper storeAfterSalesItemMapper;

    private final StoreAfterSalesStatusMapper afterSalesStsMapper;

    private final ProductService productService;

    private final IGenerator generator;

    private final KDBirdExpressService kdbirdExpressService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     * 是否需要支付取消费用
     *
     * @param yxStoreOrder
     * @param expressSigned
     */
    private boolean needPayExpressCancelFee(Order yxStoreOrder, boolean expressSigned) {
        if (yxStoreOrder.inWaiting1Hour4Delivery()) {
            return false;
        }
        /**
         * !!! 注意：出库中，需要支付10块，我们有劳动的
         */
        if (yxStoreOrder.inPrepareOuting()) {
            return true;
        }

        // 在途，需要查看快递信息
        boolean isInDelivery = yxStoreOrder.isTrueInDelivery();
        // 已签收，不用拦截了，不用扣费，由用户寄回
        if (isInDelivery && expressSigned) {
            // !!! 注意：这种情况该订单是赔的
            return false;
        }

        // 已收货订单，普通问题仍需扣费，质量问题不扣费
        // 注意：这里在申请阶段无法判断是否为质量问题，
        // 质量问题的处理在returnMoney2UserAndReportLoss中通过不重新计算退款金额来实现
        if (OrderInfoEnum.STATUS_2_DONE.equalsTo(yxStoreOrder.getStatus())) {
            return true; // 已收货状态下默认扣费，质量问题在后续处理中豁免
        }

        return true;
    }

    /**
     * 申请售后
     */
    @Override
    public void applyForAfterSales(Long userId, String nickname, StoreAfterSalesParam storeAfterSalesParam) {
        // 1. 获取订单
        String orderCode = storeAfterSalesParam.getOrderCode();

        // !! 检查是否其他订单状态锁定中，需要返回稍后重试
        String key = String.format(ShopConstants.LOCK_KEY_TMP_LOCK_ORDER_STATUS, orderCode);
        if (redisUtils.hasKey(key)) {
            throw new YshopException("订单状态锁定中，请稍后重试", true);
        }

        Order yxStoreOrder = storeOrderMapper.selectOne(Wrappers.<Order>lambdaQuery()
                .eq(Order::getOrderId, orderCode).eq(Order::getUid, userId));
        checkOrder(yxStoreOrder);

        // 2. 计算退货总价，根据sku和数量决定
        BigDecimal refundAmount = BigDecimal.ZERO;

        // 2.1 货品实际上是否已经 交给快递 发货
        boolean inWaiting1Hour4Delivery = yxStoreOrder.inWaiting1Hour4Delivery();
        boolean inPrepareOuting = yxStoreOrder.inPrepareOuting();

        /** 本地系统中订单在途 */
        boolean isTrueInDelivery = yxStoreOrder.isTrueInDelivery();

        boolean signed = false;

        // 3. 判断售后类型
        // 不是在途中且订单尚未收货，则为仅退款
        int serviceType;
        if (inWaiting1Hour4Delivery || inPrepareOuting) {
            serviceType = AfterSales.TYPE_ONLY_MONEY;
        } else if (yxStoreOrder.isReceived()) {
            // 已收货状态
            serviceType = AfterSales.TYPE_BOTH;
            signed = true;
        } else if (isTrueInDelivery) {
            // 在途状态

            // 可能需要拦截
            // 已发货分为两种情况：
            // 1. 签收：若已经收货，则为退货退款，需要扣掉10元；
            // 2. 在途，若尚未收货，则为仅退款，测试需要扣费10元

            // 查询快递物流信息，确认售后种类
            // 在途，查看当前签收情况
            // !!!查询到未签收，实际已签收，应当走退货退款
            // !!!这个误差在几分钟目前我们不能判断，所以在客服审核时候需要再次查询签收状态，以及物流情况确定
            try {
                String lastFourNumber = yxStoreOrder.getUserPhone();
                if (lastFourNumber.length() > 4) {
                    lastFourNumber = StrUtil.sub(lastFourNumber, lastFourNumber.length(), -4);
                }
                signed = kdbirdExpressService.isSigned(orderCode, yxStoreOrder.getDeliverySn(),
                        yxStoreOrder.getDeliveryId(),
                        lastFourNumber);

                if (signed) {
                    // 1. 已签收，退货退款
                    // 用户负责回寄
                    serviceType = AfterSales.TYPE_BOTH;
                } else {
                    // 2. 未签收，仅退款
                    // 商家进行拦截
                    serviceType = AfterSales.TYPE_ONLY_MONEY;
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("物流状态查询失败: {}", e.getMessage());
                throw e;
            }
        } else {
            log.warn("异常订单状态，订单: {} 状态: {} 暂存状态: {}", yxStoreOrder.getOrderId(), yxStoreOrder.getStatus(),
                    yxStoreOrder.getRefundStatus());
            serviceType = AfterSales.TYPE_BOTH;
        }

        // 是否退回全部商品
        boolean allSkuReturn = serviceType == AfterSales.TYPE_ONLY_MONEY;

        //
        // part 2
        // 退款金额计算
        //
        // 1. 获取订单中所有的商品
        List<OrderCartItem> yxStoreOrderCartInfos = storeOrderCartInfoMapper.selectList(
                Wrappers.<OrderCartItem>lambdaQuery().eq(OrderCartItem::getOid, yxStoreOrder.getId()));

        for (OrderCartItem orderCartItem : yxStoreOrderCartInfos) {
            Long spuID = orderCartItem.getProductId();
            // 商品的购物车信息详情是JSON存储
            YxStoreCartQueryVo cartInfo = orderCartItem.cartInfoVO();
            String skuUnique = cartInfo.getProductAttrUnique();

            // part 1. 仅退款
            if (allSkuReturn) {
                // 所有商品费用计入
                // 计算金额
                BigDecimal SKURefundAmount = NumberUtil.mul(cartInfo.getTruePrice(),
                        cartInfo.getCartNum());
                // 增加退款金额
                refundAmount = NumberUtil.add(refundAmount, SKURefundAmount);

                // 标记SKU已退
                orderCartItem.setIsAfterSales(OrderCartItem.ALREADY_APPLIED_AFTER_SALES);
                storeOrderCartInfoMapper.updateById(orderCartItem);
                continue;
            }

            // part 2. 退货退款
            // 2.1. 查看该sku是否退款
            List<SkuParam> skuReturnReqList = storeAfterSalesParam.getProductParamList();
            SkuParam skuReturnReq = skuReturnReqList.stream().filter(item -> {
                boolean found = item.getProductId().equals(spuID);
                // 如果退款的是某个规格的产品，那么
                if (found && !StringUtils.isEmpty(item.getProductAttrUnique())) {
                    found = item.getProductAttrUnique().equals(skuUnique);
                }
                return found;
            }).findFirst().orElse(null);

            if (skuReturnReq == null) {
                // 无退款，跳过
                continue;
            }

            // 2.2 退货退款
            // 计算金额
            BigDecimal SKURefundAmount = NumberUtil.mul(cartInfo.getTruePrice(),
                    BigDecimal.valueOf(skuReturnReq.getQuantity()));
            refundAmount = NumberUtil.add(refundAmount, SKURefundAmount);

            // 注意已经申请过售后的商品（SPU，不能再次申请售后）
            orderCartItem.setIsAfterSales(OrderCartItem.ALREADY_APPLIED_AFTER_SALES);
            storeOrderCartInfoMapper.updateById(orderCartItem);
        }

        // 3. 查找已有售后
        // 3.1 查看未完成的售后
        LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AfterSales::getOrderCode, storeAfterSalesParam.getOrderCode())
                // 查找未完成的售后
                .eq(AfterSales::getSalesState, AfterSales.SALES_CANCEL_STATUS_NORMAL)
                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                .orderByDesc(AfterSales::getId).last("limit 1");
        AfterSales oldAfterSales = this.getOne(wrapper);

        if (oldAfterSales != null) {
            // 不能有未完成的售后
            throw new YshopException("您有未完成的售后单", true);
        }

        // 3.2 查看已完成的售后，如果有2次无理由退货，那么不能再次申请售后
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AfterSales::getOrderCode, storeAfterSalesParam.getOrderCode())
                .orderByDesc(AfterSales::getId);
        List<AfterSales> oldAfterSalesList = this.getBaseMapper().selectList(wrapper);
        if (oldAfterSalesList.size() == 2) {
            if (redisUtils.isAfterSalesApplyOpen(orderCode)) {
                log.info("售后申请次数已经超过限制，但是已经开放，可以继续申请, orderCode: {}", orderCode);
            } else {
                throw new YshopException("售后申请次数已经超过限制", true);
            }
        }

        // 3.3 已完成售后如果是已退款或者已审批也不能再次申请
        AfterSales foundExist = oldAfterSalesList.stream().filter(x -> {
            if (x.getSalesState() == AfterSales.SALES_CANCEL_STATUS_MERCHANT_REJECT
                    || x.getSalesState() == AfterSales.SALES_CANCEL_STATUS_USER_CANCEL) {
                // 取消或者拒绝，可以继续申请
                return false;
            }
            return true;
        }).findFirst().orElse(null);

        if (foundExist != null) {
            throw new YshopException("售后已完成，不能再次申请售后", true);
        }

        if (needPayExpressCancelFee(yxStoreOrder, signed)) {
            // 测试情况，商品小于十五元，则退款金额为0
            if (refundAmount.compareTo(BigDecimal.valueOf(15)) == -1) {
                // 按商品货款退
                // 测试情况
                log.info("测试商品，价格低于15元，不执行扣10元操作");
            } else {
                // 退款、退货 且已发货，扣十元
                refundAmount = NumberUtil.sub(refundAmount, BigDecimal.valueOf(10));
            }

            // 除了货款，多收的邮费需要退给他
            // 拦截的情况下其实是退全款的
            refundAmount = NumberUtil.add(refundAmount, yxStoreOrder.getPayPostage());
        } else {
            if (serviceType == AfterSales.TYPE_BOTH) {
                refundAmount = NumberUtil.add(refundAmount, yxStoreOrder.getPayPostage());
            } else {
                // 因为没发货，直接退全部
                refundAmount = yxStoreOrder.getPayPrice();
            }
        }

        // 4. 更新订单状态
        Integer curOrderStatus = yxStoreOrder.getStatus();
        yxStoreOrder.setStatus(OrderInfoEnum.STATUS_AFTER_SALES.getValue());
        yxStoreOrder.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue());

        // 记录申请售后时的订单状态，未来可用于恢复
        yxStoreOrder.setRefundSuspendStatus(curOrderStatus);
        yxStoreOrder.setRefundReasonWap(storeAfterSalesParam.getReasonForApplication());
        yxStoreOrder.setRefundReasonWapExplain(storeAfterSalesParam.getApplicationInstructions());
        yxStoreOrder.setRefundReasonTime(new Date());
        storeOrderMapper.updateById(yxStoreOrder);

        // 5.生成售后
        // 5.1 售后信息插入
        AfterSales afterSales = new AfterSales();
        afterSales.setOrderCode(storeAfterSalesParam.getOrderCode());

        afterSales.setRefundAmount(refundAmount);
        afterSales.setServiceType(serviceType);
        afterSales.setReasons(storeAfterSalesParam.getReasonForApplication());
        afterSales.setExplains(storeAfterSalesParam.getApplicationInstructions());
        afterSales.setExplainImg(storeAfterSalesParam.getApplicationDescriptionPicture());
        // 平台方售后状态: 等待审核
        afterSales.setState(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue());
        // 客户售后取消状态：正常
        afterSales.setSalesState(AfterSales.SALES_CANCEL_STATUS_NORMAL);
        afterSales.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
        afterSales.setIsDel(0);
        afterSales.setUserId(userId);
        baseMapper.insert(afterSales);

        // 5.2 售后商品详情插入
        for (SkuParam skuVo : storeAfterSalesParam.getProductParamList()) {
            String skuUnique = skuVo.getProductAttrUnique();
            // 按照sku unique查找订单购物车条目信息
            OrderCartItem orderCartItem = yxStoreOrderCartInfos.stream().filter(item -> {
                YxStoreCartQueryVo cartInfo = item.cartInfoVO();
                boolean found = item.getProductId().equals(skuVo.getProductId());
                if (found) {
                    found = StringUtils.equals(cartInfo.getProductAttrUnique(), skuUnique);
                }
                return found;
            }).findFirst().orElse(new OrderCartItem());

            // 插入售后商品sku
            AfterSalesItem storeAfterSalesItem = new AfterSalesItem();
            storeAfterSalesItem.setStoreAfterSalesId(afterSales.getId());
            storeAfterSalesItem.setProductId(orderCartItem.getProductId());
            storeAfterSalesItem.setCartInfo(orderCartItem.getCartInfo());
            storeAfterSalesItem.setProductAttrUnique(skuUnique);
            storeAfterSalesItem.setNum(skuVo.getQuantity());
            storeAfterSalesItem.setIsDel(0);
            storeAfterSalesItemMapper.insert(storeAfterSalesItem);
        }

        // 5.3 售后操作记录插入
        AfterSalesStatus afterSalesSts = new AfterSalesStatus();
        afterSalesSts.setStoreAfterSalesId(afterSales.getId());
        afterSalesSts.setChangeType(0);
        afterSalesSts.setChangeMessage("售后订单生成");
        afterSalesSts.setChangeTime(Timestamp.valueOf(LocalDateTime.now()));
        afterSalesSts.setOperator(nickname);
        afterSalesStsMapper.insert(afterSalesSts);
    }

    /**
     * 获取售后订单的详情
     * 
     * @param orderId
     */
    @Override
    public List<OrderCartInfoVo> checkOrderDetails(String orderId) {
        // 0.1 获取订单原有的所有商品
        List<OrderCartItem> yxStoreOrderCartInfos = storeOrderCartInfoMapper
                .selectList(Wrappers.<OrderCartItem>lambdaQuery().eq(OrderCartItem::getOid, orderId));
        // 0.2 获取订单
        Order yxStoreOrder = storeOrderMapper.selectById(orderId);

        // 2. 查询 最新的售后信息
        AfterSales afterSales = baseMapper.selectOne(
                Wrappers.<AfterSales>lambdaQuery().eq(AfterSales::getOrderCode, yxStoreOrder.getOrderId())
                        .orderByDesc(AfterSales::getId).last("limit 1"));

        List<OrderCartInfoVo> yxStoreOrderCartInfoVos = new ArrayList<>();
        for (OrderCartItem yxStoreOrderCartInfo : yxStoreOrderCartInfos) {
            OrderCartInfoVo yxStoreOrderCartInfoVo = new OrderCartInfoVo();
            yxStoreOrderCartInfoVo.setId(yxStoreOrderCartInfo.getId());
            yxStoreOrderCartInfoVo.setOid(yxStoreOrderCartInfo.getOid());
            yxStoreOrderCartInfoVo.setCartId(yxStoreOrderCartInfo.getCartId());
            yxStoreOrderCartInfoVo.setProductId(yxStoreOrderCartInfo.getProductId());
            yxStoreOrderCartInfoVo.setUnique(yxStoreOrderCartInfo.getUnique());

            // 标记某SKU是否在售后状态
            yxStoreOrderCartInfoVo.setIsAfterSales(
                    yxStoreOrderCartInfo.getIsAfterSales() == null ? OrderCartItem.ALREADY_APPLIED_AFTER_SALES
                            : yxStoreOrderCartInfo.getIsAfterSales());

            YxStoreCartQueryVo cartInfo = JSONObject.parseObject(yxStoreOrderCartInfo.getCartInfo(),
                    YxStoreCartQueryVo.class);
            yxStoreOrderCartInfoVo.setCartInfo(cartInfo);

            /**
             * 计算商品剩下的可退数量
             */
            List<AfterSalesItem> storeAfterSalesItems = storeAfterSalesItemMapper
                    .selectList(
                            Wrappers.<AfterSalesItem>lambdaQuery().eq(AfterSalesItem::getStoreAfterSalesId,
                                    afterSales.getId()));

            AfterSalesItem asItem = storeAfterSalesItems.stream()
                    .filter(x -> x.getProductAttrUnique().equals(cartInfo.getProductAttrUnique()))
                    .findFirst().orElse(null);
            if (asItem != null) {
                yxStoreOrderCartInfoVo.setRefundNum(asItem.getNum());
            }

            int skuNum = cartInfo.getCartNum();

            // 商品总金额
            BigDecimal totalAmountOfGoods = NumberUtil.mul(cartInfo.getTruePrice(), BigDecimal.valueOf(skuNum));

            // 商品优惠总金额
            BigDecimal commodityDiscountAmount = NumberUtil.mul(NumberUtil.div(totalAmountOfGoods,
                    NumberUtil.sub(yxStoreOrder.getTotalPrice(), yxStoreOrder.getPayPostage())), BigDecimal.valueOf(0));

            BigDecimal refundablePrice = NumberUtil.sub(totalAmountOfGoods, commodityDiscountAmount);

            yxStoreOrderCartInfoVo.setRefundablePrice(refundablePrice);

            yxStoreOrderCartInfoVo.setReasons(afterSales.getReasons());
            yxStoreOrderCartInfoVos.add(yxStoreOrderCartInfoVo);
        }

        return yxStoreOrderCartInfoVos;

    }

    /**
     * 用户的售后列表
     * 
     * @param uid 用户
     */
    @Override
    public Map<String, Object> salesList(Long uid, Integer status, Integer page, String orderCode, Integer limit) {
        Page<AfterSales> storeAfterSalesPage = new Page<>(page, limit);
        List<Integer> integers = new ArrayList<>();
        if (status == 1) {
            // 售后中
            integers.add(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue());
            integers.add(AfterSalesStateEnum.STATUS_REVIEW_OK_1.getValue());
            integers.add(AfterSalesStateEnum.STATUS_USER_SENT_OR_WAIT_REFUND_2.getValue());
        }
        if (status == 2) {
            // 已完成
            integers.add(AfterSalesStateEnum.STATUS_DONE_3.getValue());
        }

        baseMapper.selectPage(storeAfterSalesPage, Wrappers.<AfterSales>lambdaQuery()
                .eq(uid != null, AfterSales::getUserId, uid)
                .in(status.equals(AfterSalesStateEnum.STATUS_REVIEW_OK_1.getValue()), AfterSales::getState,
                        integers)

                // 0 为查询全部
                // 其他值为查询 某些状态
                .in(!status.equals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue()), AfterSales::getState,
                        integers)

                .eq(StringUtils.isNotBlank(orderCode), AfterSales::getOrderCode, orderCode)
                .orderByDesc(AfterSales::getCreateTime)
                .eq(AfterSales::getIsDel, ShopCommonEnum.DELETE_0.getValue()));
        List<AfterSalesVo> storeAfterSalesVos = generator.convert(storeAfterSalesPage.getRecords(),
                AfterSalesVo.class);
        Map<String, Object> map = new HashMap<>();
        if (uid == null) {
            map.put("content", storeAfterSalesVos.stream().map(this::mashupCartInfoAndStatusChangeInfo)
                    .collect(Collectors.toList()));
            map.put("totalElements", storeAfterSalesPage.getTotal());
        } else {
            map.put("list", storeAfterSalesVos.stream().map(this::mashupCartInfoAndStatusChangeInfo)
                    .collect(Collectors.toList()));
            map.put("total", storeAfterSalesPage.getTotal());
            map.put("totalPage", storeAfterSalesPage.getPages());
        }
        return map;
    }

    /**
     * 获取售后对象
     * 
     * @param key orderID
     * @param id  售后id
     */
    @Override
    public AfterSalesVo getCSInfoByOrderCodeAndAfterIdAndUid(String key, Long id, Long uid) {
        LambdaQueryWrapper<AfterSales> query = Wrappers.<AfterSales>lambdaQuery().eq(id != null, AfterSales::getId, id)
                .eq(AfterSales::getOrderCode, key);
        if (uid != null && uid != 0) {
            query = query.eq(AfterSales::getUserId, uid);
        }
        AfterSales afterSales = baseMapper
                .selectOne(query);
        AfterSalesVo salesVo = generator.convert(afterSales, AfterSalesVo.class);
        // salesVo.setCloseAfterSaleTime(DateUtil.tomorrow().toTimestamp());
        return salesVo;
    }

    /**
     * 获取用户订单的全部售后信息
     *
     */
    @Override
    public List<AfterSalesVo> getCSInfoByOrderCodeAndUid(String key, Long uid) {
        List<AfterSales> afterSales = baseMapper.selectList(Wrappers.<AfterSales>lambdaQuery()
                .eq(AfterSales::getUserId, uid).eq(AfterSales::getOrderCode, key)
                .orderByDesc(AfterSales::getCreateTime));
        return generator.convert(afterSales, AfterSalesVo.class);
    }

    /**
     * 混入售后SKU信息和状态变化信息
     *
     * @param storeAfterSalesVo /
     * @return StoreAfterSalesVo /
     */
    @Override
    public AfterSalesVo mashupCartInfoAndStatusChangeInfo(AfterSalesVo storeAfterSalesVo) {
        // 1. 获取售后中的商品列表
        List<AfterSalesItem> storeAfterSalesItems = storeAfterSalesItemMapper
                .selectList(Wrappers.<AfterSalesItem>lambdaQuery().eq(AfterSalesItem::getStoreAfterSalesId,
                        storeAfterSalesVo.getId()));

        // 2. 获取商品列表对应的购物车信息
        List<YxStoreCartQueryVo> cartItemList = storeAfterSalesItems.stream()
                .map(cart -> {
                    YxStoreCartQueryVo data = JSON.parseObject(cart.getCartInfo(), YxStoreCartQueryVo.class);
                    if (data != null) {
                        AfterSalesItem asItem = storeAfterSalesItems.stream()
                                .filter(x -> com.leway.utils.StringUtils.equals(x.getProductAttrUnique(),
                                        data.getProductAttrUnique()))
                                .findFirst().orElse(null);
                        if (asItem != null) {
                            data.setRefundNum(asItem.getNum());
                        } else {
                            data.setRefundNum(0);
                        }
                    }
                    return data;
                })
                .collect(Collectors.toList());

        storeAfterSalesVo.setCartInfo(cartItemList);

        List<AfterSalesStatus> afterSalesStses = afterSalesStsMapper
                .selectList(Wrappers.<AfterSalesStatus>lambdaQuery()
                        .eq(AfterSalesStatus::getStoreAfterSalesId, storeAfterSalesVo.getId()));

        storeAfterSalesVo.setCompleteTime(afterSalesStses.stream().filter(item -> item.getChangeType() == 3)
                .findFirst().orElse(new AfterSalesStatus()).getChangeTime());
        storeAfterSalesVo.setDeliveryTime(afterSalesStses.stream().filter(item -> item.getChangeType() == 2)
                .findFirst().orElse(new AfterSalesStatus()).getChangeTime());
        storeAfterSalesVo.setAuditFailedTime(afterSalesStses.stream().filter(item -> item.getChangeType() == 4)
                .findFirst().orElse(new AfterSalesStatus()).getChangeTime());
        storeAfterSalesVo.setReviewTime(afterSalesStses.stream().filter(item -> item.getChangeType() == 1)
                .findFirst().orElse(new AfterSalesStatus()).getChangeTime());
        storeAfterSalesVo.setRevocationTime(afterSalesStses.stream().filter(item -> item.getChangeType() == 5)
                .findFirst().orElse(new AfterSalesStatus()).getChangeTime());
        return storeAfterSalesVo;
    }

    /**
     * 获取任意一个product sku
     */
    @Override
    public Product getAnyProductInAfterSales(AfterSalesVo afterSales) {
        // 1. 获取售后对应的原始订单
        Order order = storeOrderMapper.selectOne(
                Wrappers.<Order>lambdaQuery().eq(Order::getOrderId, afterSales.getOrderCode()));
        if (order == null) {
            log.warn("异常售后单单信息，订单: {} 不存在", afterSales.getOrderCode());
            return null;
        }

        List<OrderCartItem> cartItemList = storeOrderCartInfoMapper
                .selectList(
                        Wrappers.<OrderCartItem>lambdaQuery().eq(OrderCartItem::getOid, order.getId()));

        OrderCartItem cartItem = cartItemList.stream().findFirst().orElse(null);
        if (cartItem == null) {
            throw new YshopException("异常订单信息，订单中无商品");
        }

        // YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
        Long productId = cartItem.getProductId();

        Product product = productService.getById(productId);
        return product;
    }

    /**
     * 撤销售后
     * 
     * @param key order code
     * @param uid
     * @param id  售后id
     * @return
     */
    @Override
    public Boolean revoke(String key, Long uid, Long id) {
        AfterSales afterSales = baseMapper
                .selectOne(Wrappers.<AfterSales>lambdaQuery().eq(AfterSales::getUserId, uid)
                        .eq(AfterSales::getId, id).eq(AfterSales::getOrderCode, key));
        if (afterSales == null) {
            log.error("未查询到售后订单信息, sid: {}, order code: {}, user: {}", id, key, uid);
            throw new YshopException("未查询到售后订单信息");
        }
        if (afterSales.getState() > 1) {
            log.error("该售后单不能撤销, sid: {}, order code: {}, user: {}, 售后状态: {}", id, key, uid, afterSales.getState());
            throw new YshopException("该售后单不能撤销");
        }

        // 1. 设置为用户自己撤销售后
        afterSales.setSalesState(AfterSales.SALES_CANCEL_STATUS_USER_CANCEL);
        afterSales.setState(AfterSalesStateEnum.STATUS_DONE_3.getValue());

        Order yxStoreOrder = storeOrderMapper
                .selectOne(Wrappers.<Order>lambdaQuery().eq(Order::getOrderId, key));

        // 拦截状态中不可撤销售后
        Integer interruptStatus = yxStoreOrder.getInterceptStatus();
        if (interruptStatus != null && interruptStatus != 0) {
            log.error("该售后单不能撤销, sid: {}, order code: {}, user: {}", id, key, uid);
            throw new YshopException("该售后单不能撤销");
        }

        // 设置原先的订单状态
        // 待发货时，撤销售后，订单状态回到待支付
        // 待收货时，不适用（因为已经发货了，不能撤销售后，必须先收货再退货退款）
        // 已收货时(待评价），撤销售后，订单状态回到已收货(待评价)
        // 已完成时，不适用，不能进行售后
        Integer refundSuspendStatus = yxStoreOrder.getRefundSuspendStatus();
        yxStoreOrder.setStatus(refundSuspendStatus);

        // 2. 订单的退款状态回到正常
        yxStoreOrder.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());
        storeOrderMapper.updateById(yxStoreOrder);

        // 3. 将订单中的商品回到可以售后的状态
        List<OrderCartItem> yxStoreOrderCartInfos = storeOrderCartInfoMapper.selectList(
                Wrappers.<OrderCartItem>lambdaQuery().eq(OrderCartItem::getOid, yxStoreOrder.getId()));
        for (OrderCartItem yxStoreOrderCartInfo : yxStoreOrderCartInfos) {
            yxStoreOrderCartInfo.setIsAfterSales(OrderCartItem.CAN_APPLY_AFTER_SALES);
            storeOrderCartInfoMapper.updateById(yxStoreOrderCartInfo);
        }

        // 操作记录
        AfterSalesStatus afterSalesSts = new AfterSalesStatus();
        afterSalesSts.setStoreAfterSalesId(afterSales.getId());
        afterSalesSts.setChangeType(AfterSalesStatus.TYPE_USER_CANCEL);
        afterSalesSts.setChangeMessage("用户撤销");
        afterSalesSts.setChangeTime(Timestamp.valueOf(LocalDateTime.now()));
        afterSalesSts.setOperator("用户操作");
        afterSalesStsMapper.insert(afterSalesSts);

        return baseMapper.updateById(afterSales) > 0;
    }

    /**
     * 添加售后快递信息
     * 
     * @param code       物流公司编码
     * @param name       物流公司名称
     * @param postalCode 快递单号
     * @param phone      phone
     * @param orderCode  订单号
     * @return 操作结果
     */
    @Override
    public Boolean addLogisticsInformation(String code, String name, String postalCode, String phone,
            String orderCode) {
        AfterSales afterSales = baseMapper.selectOne(Wrappers.<AfterSales>lambdaQuery()
                .eq(AfterSales::getOrderCode, orderCode).orderByDesc(AfterSales::getId).last("limit 1"));

        // 平台审核通过的状态才能添加退货物流信息
        if (!afterSales.getState().equals(AfterSalesStateEnum.STATUS_REVIEW_OK_1.getValue())) {
            throw new YshopException("当前状态不能添加物流信息!");
        }

        // 1. 更新售后信息
        afterSales.setDeliverySn(code);
        afterSales.setDeliveryName(name);
        afterSales.setDeliveryId(postalCode);
        afterSales.setDeliveryPhone(phone);
        afterSales.setState(AfterSalesStateEnum.STATUS_USER_SENT_OR_WAIT_REFUND_2.getValue());

        // 2. 增加操作记录
        AfterSalesStatus afterSalesSts = new AfterSalesStatus();
        afterSalesSts.setStoreAfterSalesId(afterSales.getId());
        afterSalesSts.setChangeType(2);
        afterSalesSts.setChangeMessage("用户发货");
        afterSalesSts.setChangeTime(Timestamp.valueOf(LocalDateTime.now()));
        afterSalesSts.setOperator("用户操作");
        afterSalesStsMapper.insert(afterSalesSts);

        return baseMapper.updateById(afterSales) > 0;
    }

    @Override
    public boolean closeExchangeOrder(Long salesId) {
        AfterSales afterSales = baseMapper.selectOne(Wrappers.<AfterSales>lambdaQuery()
                .eq(AfterSales::getId, salesId));

        if (ObjectUtils.isEmpty(afterSales)) {
            throw new ErrorRequestException("售后单不存在");
        }

        if (afterSales.getState().equals(AfterSalesStateEnum.STATUS_DONE_3.getValue())) {
            throw new ErrorRequestException("售后单已完成");
        }

        if (afterSales.getState().equals(AfterSalesStateEnum.STATUS_DONE_WITH_ISSUE_4.getValue())) {
            throw new ErrorRequestException("售后单已完成");
        }

        if (afterSales.getState().equals(AfterSalesStateEnum.STATUS_DONE_WITH_EXCHANGE_5.getValue())) {
            return true;
        }

        afterSales.setState(AfterSalesStateEnum.STATUS_DONE_WITH_EXCHANGE_5.getValue());
        baseMapper.updateById(afterSales);

        return true;
    }

    /**
     * 标记商品无法二次销售
     */
    @Override
    public boolean markGoodsBroken(Long salesId) {
        AfterSales afterSales = baseMapper.selectOne(Wrappers.<AfterSales>lambdaQuery()
                .eq(AfterSales::getId, salesId));

        if (ObjectUtils.isEmpty(afterSales)) {
            throw new ErrorRequestException("售后单不存在");
        }

        if (afterSales.getState().equals(AfterSalesStateEnum.STATUS_DONE_3.getValue())) {
            throw new ErrorRequestException("售后单已完成");
        }

        if (afterSales.getState().equals(AfterSalesStateEnum.STATUS_DONE_WITH_EXCHANGE_5.getValue())) {
            throw new ErrorRequestException("售后单已经完成换货");
        }

        if (afterSales.getState().equals(AfterSalesStateEnum.STATUS_DONE_WITH_ISSUE_4.getValue())) {
            return true;
        }

        afterSales.setState(AfterSalesStateEnum.STATUS_DONE_WITH_ISSUE_4.getValue());
        baseMapper.updateById(afterSales);

        return true;
    }

    /**
     * 删除售后订单
     */
    @Override
    public Boolean deleteAfterSalesOrder(String orderCode, Long id) {
        AfterSales afterSales = baseMapper.selectOne(Wrappers.<AfterSales>lambdaQuery()
                .eq(AfterSales::getId, id).eq(AfterSales::getOrderCode, orderCode));
        return baseMapper.deleteById(afterSales.getId()) > 0;
    }

    /**
     * 商家审核：通过或拒绝
     */
    @Override
    public boolean salesCheck(Long salesId, String orderCode, Integer approvalStatus, String consignee,
            String phoneNumber, String address, String operator, String rejectReason) {
        AfterSales afterSales = baseMapper.selectOne(Wrappers.<AfterSales>lambdaQuery()
                .eq(AfterSales::getOrderCode, orderCode).eq(AfterSales::getId, salesId));
        // 通过申请
        if (approvalStatus == AfterSales.APPROVED) {
            // 1. 通过审批
            // 通过申请，标记售后完成
            if (afterSales.getServiceType() == AfterSales.TYPE_BOTH) {
                afterSales.setState(AfterSalesStateEnum.STATUS_REVIEW_OK_1.getValue());
                // 1. 退货退款
                if (StringUtils.isEmpty(consignee) || StringUtils.isEmpty(phoneNumber)
                        || StringUtils.isEmpty(address)) {
                    throw new ErrorRequestException("请输入收货人信息");
                }
                afterSales.setConsignee(consignee);
                afterSales.setPhoneNumber(phoneNumber);
                afterSales.setAddress(address);
            } else {
                // 0. 仅退款
                // returnMoney2User(afterSales.getId(), orderCode);

                // 库存返回
                afterSales.setState(AfterSalesStateEnum.STATUS_USER_SENT_OR_WAIT_REFUND_2.getValue());
            }

            // 操作记录
            AfterSalesStatus afterSalesSts = new AfterSalesStatus();
            afterSalesSts.setStoreAfterSalesId(afterSales.getId());
            afterSalesSts.setChangeType(1);
            afterSalesSts.setChangeMessage("平台审核成功");
            afterSalesSts.setChangeTime(Timestamp.valueOf(LocalDateTime.now()));
            afterSalesSts.setOperator(operator);
            afterSalesStsMapper.insert(afterSalesSts);

        } else {
            // 拒绝
            afterSales.setState(AfterSalesStateEnum.STATUS_DONE_3.getValue());
            afterSales.setSalesState(AfterSales.SALES_CANCEL_STATUS_MERCHANT_REJECT);
            afterSales.setRejectReason(rejectReason);

            // FIXME:这里要恢复订单状态: order status
            // Order yxStoreOrder = storeOrderMapper.selectOne(
            // Wrappers.<Order>lambdaQuery().eq(Order::getOrderId,
            // afterSales.getOrderCode()));
            // Integer refundSuspendStatus = yxStoreOrder.getRefundSuspendStatus();
            // yxStoreOrder.setStatus(refundSuspendStatus);

            // storeOrderMapper.updateById(yxStoreOrder);

            // 操作记录
            AfterSalesStatus afterSalesSts = new AfterSalesStatus();
            afterSalesSts.setStoreAfterSalesId(afterSales.getId());
            afterSalesSts.setChangeType(4);
            afterSalesSts.setChangeMessage("平台审核失败");
            afterSalesSts.setChangeTime(Timestamp.valueOf(LocalDateTime.now()));
            afterSalesSts.setOperator(operator);
            afterSalesStsMapper.insert(afterSalesSts);
        }
        return baseMapper.updateById(afterSales) > 0;
    }

    /**
     * 更新售后状态
     * 
     * @param salesId
     * @param state
     * @return
     */
    private AfterSales _updateAfterSalesState(Long salesId, AfterSalesStateEnum state) {
        AfterSales afterSales = baseMapper.selectOne(Wrappers.<AfterSales>lambdaQuery()
                .eq(AfterSales::getId, salesId));
        afterSales.setState(state.getValue());
        baseMapper.updateById(afterSales);
        return afterSales;
    }

    /**
     * 添加售后打款成功记录
     * 
     * @param salesId
     * @param operator
     * @return
     */
    private AfterSalesStatus _addPaySuccessRecord(Long salesId, String operator) {
        AfterSalesStatus afterSalesSts = new AfterSalesStatus();
        afterSalesSts.setStoreAfterSalesId(salesId);
        afterSalesSts.setChangeType(AfterSalesStatus.TYPE_PAY);
        afterSalesSts.setChangeMessage("平台打款成功");
        afterSalesSts.setChangeTime(Timestamp.valueOf(LocalDateTime.now()));
        afterSalesSts.setOperator(operator);
        afterSalesStsMapper.insert(afterSalesSts);
        return afterSalesSts;
    }

    /**
     * 根据售后订单退款
     * !!!不涉及库存立刻回滚，需要微信真正退款回调完成退款 handleWechatRefundCallback
     * 影响：
     * 1. 售后表
     * 2. 售后操作记录表
     * 3. 订单表(到退款中)
     */
    @Override
    public AfterSales returnMoney2User(Long salesId, String orderCode, String operator) {
        // 0. 获取售后记录和订单信息
        AfterSales afterSales = baseMapper.selectById(salesId);
        Order yxStoreOrder = storeOrderMapper.selectOne(
                Wrappers.<Order>lambdaQuery().eq(Order::getOrderId, afterSales.getOrderCode()));

        // 0.1 重新计算退款金额 - 普通问题需要扣费
        BigDecimal adjustedRefundAmount = recalculateRefundAmountForNormalIssue(afterSales, yxStoreOrder);
        afterSales.setRefundAmount(adjustedRefundAmount);

        // 0.2 修改售后状态到已完成
        afterSales = _updateAfterSalesState(salesId, AfterSalesStateEnum.STATUS_DONE_3);

        // 0.3 更新退款金额
        afterSales.setRefundAmount(adjustedRefundAmount);
        baseMapper.updateById(afterSales);

        // 1.操作记录
        _addPaySuccessRecord(afterSales.getId(), operator);

        /**
         * !!!微信退款中，因为微信支付服务不在这里，以微信最终退款回调为准
         */
        yxStoreOrder.setWechatRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue());
        yxStoreOrder.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_2_DONE.getValue());
        yxStoreOrder.setFinishTime(new Date());

        storeOrderMapper.updateById(yxStoreOrder);

        return afterSales;
    }

    /**
     * 更新售后状态到已完成，并标记报损
     * 
     * @param salesId
     */
    private AfterSales _updateAndMarkLoss(Long salesId) {
        AfterSales afterSales = baseMapper.selectOne(Wrappers.<AfterSales>lambdaQuery().eq(AfterSales::getId, salesId));
        afterSales.setState(AfterSalesStateEnum.STATUS_DONE_3.getValue());
        // !!! 标记是报损，后续执行库存回退时立刻报损
        afterSales.setLossStatus(AfterSales.LOSS_STATUS_YES);
        baseMapper.updateById(afterSales);
        return afterSales;
    }

    /**
     * 执行微信支付退款的前一步操作
     * !!!不涉及库存立刻回滚，需要微信真正退款回调完成退款 handleWechatRefundCallback
     */
    @Override
    public AfterSales returnMoney2UserAndReportLoss(Long salesId, String orderCode, String operator) {
        // 0. 获取售后记录和订单信息
        AfterSales afterSales = baseMapper.selectById(salesId);
        Order yxStoreOrder = storeOrderMapper.selectOne(
                Wrappers.<Order>lambdaQuery().eq(Order::getOrderId, afterSales.getOrderCode()));

        // 0.1 重新计算退款金额 - 质量问题不扣费
        BigDecimal adjustedRefundAmount = recalculateRefundAmountForQualityIssue(afterSales, yxStoreOrder);
        afterSales.setRefundAmount(adjustedRefundAmount);

        // 0.2 修改售后状态并标记报损
        afterSales = _updateAndMarkLoss(salesId);

        // 0.3 更新退款金额
        afterSales.setRefundAmount(adjustedRefundAmount);
        baseMapper.updateById(afterSales);

        // 1.操作记录
        _addPaySuccessRecord(afterSales.getId(), operator);

        /**
         * !!!微信退款中，因为微信支付服务不在这里，以微信最终退款回调为准
         */
        yxStoreOrder.setWechatRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue());
        yxStoreOrder.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_2_DONE.getValue());
        yxStoreOrder.setFinishTime(new Date());

        storeOrderMapper.updateById(yxStoreOrder);

        return afterSales;
    }

    /**
     * 检查订单是否符合售后订单
     *
     * @param yxStoreOrder 订单
     */
    private void checkOrder(Order yxStoreOrder) {
        if (yxStoreOrder == null) {
            throw new YshopException("未查询到订单信息");
        }

        // 1. 未支付
        // 2. 正在申请售后
        // 3. 异常状态
        if (yxStoreOrder.notPaid()
                || !yxStoreOrder.getRefundStatus().equals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue())
                || yxStoreOrder.getStatus() < OrderInfoEnum.STATUS_NORMAL_0.getValue()) {
            throw new YshopException("订单状态不能售后");
        }
    }

    @Override
    public PageResult<AfterSalesVo> queryAll(AfterSalesQueryCriteria criteria, Pageable pageable) {
        return queryAll(criteria, pageable, true);
    }

    @Override
    public PageResult<AfterSalesVo> queryAll(AfterSalesQueryCriteria criteria, Pageable pageable, boolean mashup) {
        getPageByStartPage0(pageable);

        LambdaQueryWrapper<AfterSales> queryWrapper = Wrappers.<AfterSales>lambdaQuery()
                .in(ObjectUtils.isNotEmpty(criteria.getState()), AfterSales::getState, criteria.getState())
                .eq(StringUtils.isNotBlank(criteria.getOrderCode()), AfterSales::getOrderCode,
                        criteria.getOrderCode())
                .eq(ObjectUtils.isNotEmpty(criteria.getSalesState()), AfterSales::getSalesState,
                        criteria.getSalesState())
                .eq(ObjectUtils.isNotEmpty(criteria.getServiceType()), AfterSales::getServiceType,
                        criteria.getServiceType())
                .eq(AfterSales::getIsDel, ShopCommonEnum.DELETE_0.getValue());

        if (CollectionUtils.isNotEmpty(criteria.getTime())) {
            queryWrapper.ge(AfterSales::getCreateTime, criteria.getTime().get(0))
                    .le(AfterSales::getCreateTime, criteria.getTime().get(1));
        }
        queryWrapper = queryWrapper.orderByDesc(AfterSales::getCreateTime);

        // IPage<AfterSales> pageResult = baseMapper.selectPage(pageModel,
        // queryWrapper);
        PageInfo<AfterSales> pageResult = new PageInfo<>(baseMapper.selectList(queryWrapper));
        List<AfterSalesVo> storeAfterSalesVos = generator.convert(pageResult.getList(),
                AfterSalesVo.class);

        PageResult<AfterSalesVo> ret = new PageResult<AfterSalesVo>();
        List<AfterSalesVo> retList = storeAfterSalesVos;
        if (mashup) {
            retList = storeAfterSalesVos.stream().map(this::mashupCartInfoAndStatusChangeInfo)
                    .collect(Collectors.toList());
        }
        ret.setContent(retList);
        ret.setTotalElements(pageResult.getTotal());
        return ret;
    }

    @Override
    public List<AfterSales> queryAll(AfterSalesQueryCriteria criteria) {
        Wrapper<AfterSales> queryWrapper = QueryHelpPlus.getPredicate(AfterSales.class, criteria);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void download(List<AfterSalesDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (AfterSalesDto yxStoreAfterSales : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("订单号", yxStoreAfterSales.getOrderCode());
            map.put("退款金额", yxStoreAfterSales.getRefundAmount());
            map.put("服务类型0仅退款1退货退款", yxStoreAfterSales.getServiceType());
            map.put("申请原因", yxStoreAfterSales.getReasons());
            map.put("说明", yxStoreAfterSales.getExplains());
            map.put("说明图片->多个用逗号分割", yxStoreAfterSales.getExplainImg());
            map.put("物流公司编码", yxStoreAfterSales.getShipperCode());
            map.put("物流单号", yxStoreAfterSales.getDeliverySn());
            map.put("物流名称", yxStoreAfterSales.getDeliveryName());
            map.put("状态 0已提交等待平台审核 1平台已审核 等待用户发货/退款 2 用户已发货 3退款成功", yxStoreAfterSales.getState());
            map.put("售后状态-0正常1用户取消2商家拒绝", yxStoreAfterSales.getSalesState());
            map.put("添加时间", yxStoreAfterSales.getCreateTime());
            map.put("逻辑删除", yxStoreAfterSales.getIsDel());
            map.put("用户id", yxStoreAfterSales.getUserId());
            map.put("商家收货人", yxStoreAfterSales.getConsignee());
            map.put("商家手机号", yxStoreAfterSales.getPhoneNumber());
            map.put("商家地址", yxStoreAfterSales.getAddress());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    /**
     * 重新计算普通问题的退款金额（需要扣费）
     *
     * @param afterSales 售后记录
     * @param yxStoreOrder 订单信息
     * @return 调整后的退款金额
     */
    private BigDecimal recalculateRefundAmountForNormalIssue(AfterSales afterSales, Order yxStoreOrder) {
        BigDecimal originalRefundAmount = afterSales.getRefundAmount();

        // 如果是已收货状态的退货退款，普通问题需要扣10元
        if (OrderInfoEnum.STATUS_2_DONE.equalsTo(yxStoreOrder.getStatus()) &&
            afterSales.getServiceType() == AfterSales.TYPE_BOTH) {

            // 检查是否已经扣过费了
            BigDecimal expectedFullRefund = calculateExpectedFullRefund(yxStoreOrder, afterSales);

            // 如果当前退款金额等于全额退款，说明没有扣费，需要扣10元
            if (originalRefundAmount.compareTo(expectedFullRefund) == 0) {
                // 扣除10元，但不能低于0
                BigDecimal adjustedAmount = originalRefundAmount.subtract(new BigDecimal("10"));
                return adjustedAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : adjustedAmount;
            }
        }

        // 其他情况保持原退款金额
        return originalRefundAmount;
    }

    /**
     * 计算期望的全额退款金额
     *
     * @param yxStoreOrder 订单信息
     * @param afterSales 售后记录
     * @return 期望的全额退款金额
     */
    private BigDecimal calculateExpectedFullRefund(Order yxStoreOrder, AfterSales afterSales) {
        // 获取订单的购物车信息
        List<OrderCartItem> cartItems = storeOrderCartInfoMapper.selectList(
                Wrappers.<OrderCartItem>lambdaQuery().eq(OrderCartItem::getOid, yxStoreOrder.getId()));

        BigDecimal totalProductAmount = BigDecimal.ZERO;

        // 计算退货商品的总金额
        for (OrderCartItem cartItem : cartItems) {
            if (cartItem.getIsAfterSales() == OrderCartItem.ALREADY_APPLIED_AFTER_SALES) {
                YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
                BigDecimal itemAmount = new BigDecimal(cartInfo.getTruePrice()).multiply(new BigDecimal(cartInfo.getCartNum()));
                totalProductAmount = totalProductAmount.add(itemAmount);
            }
        }

        // 全额退款 = 商品金额 + 邮费
        return totalProductAmount.add(yxStoreOrder.getPayPostage());
    }

    /**
     * 重新计算质量问题的退款金额（不扣费）
     *
     * @param afterSales 售后记录
     * @param yxStoreOrder 订单信息
     * @return 调整后的退款金额
     */
    private BigDecimal recalculateRefundAmountForQualityIssue(AfterSales afterSales, Order yxStoreOrder) {
        BigDecimal originalRefundAmount = afterSales.getRefundAmount();

        // 如果是已收货状态的退货退款，质量问题需要恢复被扣除的10元
        if (OrderInfoEnum.STATUS_2_DONE.equalsTo(yxStoreOrder.getStatus()) &&
            afterSales.getServiceType() == AfterSales.TYPE_BOTH) {

            // 计算期望的全额退款金额
            BigDecimal expectedFullRefund = calculateExpectedFullRefund(yxStoreOrder, afterSales);

            log.info("质量问题退款金额重新计算 - 原退款金额: {}, 期望全额退款: {}", originalRefundAmount, expectedFullRefund);

            // 质量问题应该退全款（商品金额+邮费），不扣10元
            // 直接返回期望的全额退款金额
            return expectedFullRefund;
        }

        // 其他情况保持原退款金额
        return originalRefundAmount;
    }

}

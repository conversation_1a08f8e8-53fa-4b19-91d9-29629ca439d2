/**
 * Copyright (C) 2019-2023
 * All rights reserved, <NAME_EMAIL>
 * 注意：
 * 本软件为************开发研制，未经购买不得使用
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.leway.modules.order.service.impl;

import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import com.leway.modules.tools.express.vo.*;

import org.apache.poi.hpsf.Decimal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.leway.api.JDLimitException;
import com.leway.api.ResultWithMsg;
import com.leway.api.YshopException;
import com.leway.common.service.impl.BaseServiceImpl;
import com.leway.common.util.SnowFlakeService;
import com.leway.common.utils.QueryHelpPlus;
import com.leway.modules.shop.service.YxSystemConfigService;
import com.leway.constant.ShopConstants;
import com.leway.constant.SystemConfigConstants;
import com.leway.domain.PageResult;
import com.leway.dozer.service.IGenerator;
import com.leway.enums.AfterSalesStateEnum;
import com.leway.enums.BillDetailEnum;
import com.leway.enums.ClientOrderStatusEnum;
import com.leway.enums.OrderInfoEnum;
import com.leway.enums.OrderLogEnum;
import com.leway.enums.OrderRefundStatusEnum;
import com.leway.enums.PayStsEnum;
import com.leway.enums.PayTypeEnum;
import com.leway.enums.ShipperCodeEnum;
import com.leway.enums.ShippingTempEnum;
import com.leway.event.TemplateBean;
import com.leway.event.TemplateEvent;
import com.leway.event.TemplateListenEnum;
import com.leway.exception.BadRequestException;
import com.leway.exception.EntityExistException;
//import com.leway.modules.activity.domain.YxStoreCouponUser;
//import com.leway.modules.activity.domain.YxStorePink;
//import com.leway.modules.activity.vo.StoreCouponUserVo;
import com.leway.modules.cart.domain.YxStoreCart;
import com.leway.modules.cart.service.CartFullVo;
import com.leway.modules.cart.service.YxStoreCartService;
import com.leway.modules.cart.service.mapper.StoreCartMapper;
import com.leway.modules.cart.vo.YxStoreCartQueryVo;
import com.leway.modules.inventory.domain.InventoryRecord;
import com.leway.modules.inventory.domain.InventoryTransaction;
import com.leway.modules.inventory.domain.TmpInventory;
import com.leway.modules.inventory.domain.TmpInventoryDailySummary;
import com.leway.modules.inventory.service.InventoryRecordService;
import com.leway.modules.inventory.service.InventoryTransactionService;
import com.leway.modules.mp.service.WeiXinOrderShippingService;
import com.leway.modules.order.domain.Express;
import com.leway.modules.order.domain.Order;
import com.leway.modules.order.domain.OrderCartItem;
import com.leway.modules.order.domain.OrderStatus;
import com.leway.modules.order.param.OrderParam;
import com.leway.modules.order.service.ExpressService;
import com.leway.modules.order.service.OrderCartInfoService;
import com.leway.modules.order.service.OrderStatusService;
import com.leway.modules.order.service.StoreOrderService;
import com.leway.modules.order.service.dto.OrderInfoCacheDto;
import com.leway.modules.order.service.dto.CountDto;
import com.leway.modules.order.service.dto.OrderCountDto;
import com.leway.modules.order.service.dto.OrderTimeDataDto;
import com.leway.modules.order.service.dto.PriceGroupDto;
import com.leway.modules.order.service.dto.StatusDto;
import com.leway.modules.order.service.dto.StoreOrderCartInfoDto;
import com.leway.modules.order.service.dto.TemplateDto;
import com.leway.modules.order.service.dto.YxStoreOrderDto;
import com.leway.modules.order.service.dto.YxStoreOrderQueryCriteria;
import com.leway.modules.order.service.dto.YxStoreOrderStatusDto;
import com.leway.modules.order.service.mapper.OrderCartItemMapper;
import com.leway.modules.order.service.mapper.OrderMapper;
import com.leway.modules.order.vo.ComputeVo;
import com.leway.modules.order.vo.ConfirmOrderVo;
import com.leway.modules.order.vo.OrderDataVo;
import com.leway.modules.order.vo.ShoperOrderTimeDataVo;
import com.leway.modules.order.vo.UserOrderCountVo;
import com.leway.modules.order.vo.YxStoreOrderQueryVo;
import com.leway.modules.product.domain.Product;
import com.leway.modules.product.domain.SKU;
import com.leway.modules.product.service.ProductService;
import com.leway.modules.product.service.SKUService;
import com.leway.modules.product.service.TmpInventoryService;
import com.leway.modules.product.service.TmpInventorySummaryService;
import com.leway.modules.product.vo.ProductQueryVo;
import com.leway.modules.sales.domain.AfterSales;
import com.leway.modules.sales.domain.AfterSalesItem;
import com.leway.modules.sales.param.AfterSalesQueryCriteria;
import com.leway.modules.sales.service.AfterSalesService;
import com.leway.modules.sales.service.mapper.StoreAfterSalesItemMapper;
import com.leway.modules.sales.service.vo.AfterSalesVo;
import com.leway.modules.template.domain.YxShippingTemplates;
import com.leway.modules.template.domain.YxShippingTemplatesFree;
import com.leway.modules.template.domain.YxShippingTemplatesRegion;
import com.leway.modules.template.domain.YxSystemCity;
import com.leway.modules.template.service.YxShippingTemplatesFreeService;
import com.leway.modules.template.service.YxShippingTemplatesRegionService;
import com.leway.modules.template.service.YxShippingTemplatesService;
import com.leway.modules.template.service.YxSystemCityService;
import com.leway.modules.tools.express.KDBirdExpressService;
import com.leway.modules.tools.express.config.ExpressAutoConfiguration;
import com.leway.modules.user.domain.YxUser;
import com.leway.modules.user.domain.YxUserAddress;
import com.leway.modules.user.service.YxUserAddressService;
import com.leway.modules.user.service.YxUserBillService;
import com.leway.modules.user.service.YxUserService;
import com.leway.modules.user.service.dto.YxUserDto;
import com.leway.modules.user.vo.YxUserQueryVo;
import com.leway.utils.DateUtils;
import com.leway.utils.FileUtil;
import com.leway.utils.OrderUtil;
import com.leway.utils.RedisLock;
import com.leway.utils.RedisUtil;
import com.leway.utils.RedisUtils;
import com.leway.utils.StringUtils;
import com.leway.utils.spring.SortPage;
import com.vdurmont.emoji.EmojiParser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020-05-12
 */
@Slf4j
@Service
@CacheConfig(cacheNames = "#60")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class StoreOrderServiceImpl extends BaseServiceImpl<OrderMapper, Order>
        implements StoreOrderService {
    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private IGenerator generator;

    @Autowired
    private SnowFlakeService snowFlakeService;

    @Autowired
    private SKUService skuService;

    @Autowired
    private WeiXinOrderShippingService weiXinOrderShippingService;

    @Autowired
    private TmpInventoryService tmpInventoryService;

    @Autowired
    private TmpInventorySummaryService tmpInventorySummaryService;

    @Autowired
    private OrderCartInfoService orderCartInfoService;

    @Autowired
    private YxStoreCartService storeCartService;

    @Autowired
    private YxUserAddressService userAddressService;

    @Autowired
    private OrderStatusService orderStatusService;

    @Autowired
    private YxUserBillService billService;

    @Autowired
    private YxUserService userService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ExpressService expressService;

    // @Autowired
    // private AlipayConfigService alipayService;

    @Autowired
    private YxStoreCartService yxStoreCartService;

    @Autowired
    private YxShippingTemplatesService shippingTemplatesService;

    @Autowired
    private YxShippingTemplatesRegionService shippingTemplatesRegionService;

    @Autowired
    private YxShippingTemplatesFreeService shippingTemplatesFreeService;

    @Autowired
    private OrderCartItemMapper storeOrderCartInfoMapper;

    @Autowired
    private YxSystemConfigService systemConfigService;

    // @Autowired
    // private YxUserLevelService userLevelService;

    @Autowired
    private OrderMapper yxStoreOrderMapper;

    @Autowired
    private YxSystemCityService systemCityService;

    @Autowired
    private StoreCartMapper storeCartMapper;

    @Autowired
    private RedisUtils redisUtils;

    // @Autowired
    // private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private AfterSalesService afterSalesService;

    @Autowired
    private StoreAfterSalesItemMapper storeAfterSalesItemMapper;

    @Autowired
    private InventoryRecordService inventoryRecordService;

    @Autowired
    private InventoryTransactionService inventoryTransactionService;

    /**
     * 返回订单确认数据
     *
     * @param yxUser  yxUser
     * @param cartIds 购物车ids，逗号分隔
     * @return ConfirmOrderVO
     */
    @Override
    public ConfirmOrderVo confirmOrder(YxUser yxUser, String cartIds, Long addressId) {
        Long uid = yxUser.getUid();

        // 1. 获取有效商品、和失效商品
        CartFullVo cartGroup = yxStoreCartService.getUserProductCartList(uid,
                cartIds, false);

        if (ObjectUtil.isNotEmpty(cartGroup.getInvalid())) {
            throw new YshopException("包含已失效商品，无效下单");
        }

        if (ObjectUtil.isEmpty(cartGroup.getValid())) {
            throw new YshopException("请提交购买的商品");
        }

        // 2. 明确欲购买的商品，及sku
        List<YxStoreCartQueryVo> cartInfo = cartGroup.getValid();

        // 3. 获取地址
        YxUserAddress userAddress = null;
        if (addressId == null || addressId == 0) {
            throw new YshopException("请选择收货地址");
        }

        userAddress = userAddressService.getById(addressId);
        if (ObjectUtil.isNull(userAddress)) {
            throw new YshopException("地址信息有误，请重新选择");
        }

        // 4. 根据收货地址、获取价格
        PriceGroupDto priceGroup = this.getOrderPriceGroup(cartInfo, userAddress);

        Map<Long, Integer> spuAlreadyBuyMap = new HashMap<>();
        Map<Long, Integer> spuCartNumMap = new HashMap<>();
        // 获取用户当前购物车中的SPU购买总数
        for (YxStoreCartQueryVo cart : cartInfo) {
            spuAlreadyBuyMap.put(cart.getProductId(), 0);
            Integer cartNum = spuCartNumMap.get(cart.getProductId());
            if (cartNum == null) {
                spuCartNumMap.put(cart.getProductId(), cart.getCartNum());
            } else {
                spuCartNumMap.put(cart.getProductId(), cartNum + cart.getCartNum());
            }

        }

        // 获取用户今日已购买SPU的订单中商品数量
        for (Entry<Long, Integer> entry : spuCartNumMap.entrySet()) {
            Long productId = entry.getKey();
            int todayProductBuyNum = redisUtils.getDailyPurchaseNum(uid, productId);
            spuAlreadyBuyMap.put(productId, todayProductBuyNum);
        }

        // 5. 检测库存
        for (YxStoreCartQueryVo cart : cartInfo) {
            ProductQueryVo product = cart.getProductInfo();
            // 检测SKU库存
            yxStoreCartService.checkProductStock(uid, cart.getProductId(), cart.getCartNum(),
                    cart.getProductAttrUnique());

            // 限购
            // SPU 层面限制购买数量，实际发生在SPU层面
            Integer productLimitBuy = product.getLimitBuy();
            Integer productCartNum = spuCartNumMap.get(cart.getProductId());
            if (productLimitBuy != null && productLimitBuy > 0) {
                // 3.1 单次下单限购
                if (productCartNum > productLimitBuy) {
                    String message = "商品" + product.getStoreName() + "超过限购数量";
                    throw new YshopException(message, true);
                }

                // 3.2 自然天限购，防止换牛快速囤货
                // 获取用户当日已购买的SPU的订单中商品数量
                int todayProductBuyNum = spuAlreadyBuyMap.get(cart.getProductId());
                // 3.2.1 获取当日用户该商品全部支付过且无退款的订单
                if (todayProductBuyNum + productCartNum > productLimitBuy) {
                    String message = "商品" + product.getStoreName() + "超过今日限购数量";
                    throw new YshopException(message, true);
                }
            }

            // b. SKU 限购
            SKU sku = cart.getProductInfo().getAttrInfo();
            Integer skuLimitBuy = sku.getLimitBuy();
            int todaySkuBuyNum = redisUtils.getDailySkuPurchaseNum(uid, cart.getProductId(),
                    cart.getProductAttrUnique());
            if (skuLimitBuy != null && skuLimitBuy > 0) {
                if (cart.getCartNum() + todaySkuBuyNum > skuLimitBuy) {
                    String message = String.format("商品%s-%s超过限购数量，今日已购买%d件，限购%d件", product.getStoreName(), sku.getSku(),
                            todaySkuBuyNum, skuLimitBuy);
                    throw new YshopException(message, true);
                }
            }
        }

        //
        //
        // 6. 缓存用户即将下单的购物信息，获取订单的下单key
        //
        //
        String cacheKey = this.cacheOrderInfo(uid, cartInfo, priceGroup);

        return ConfirmOrderVo.builder()
                .canPay(true)
                .msg("OK")
                .addressInfo(userAddress)
                .cartInfo(cartInfo)
                .priceGroup(priceGroup)
                .userInfo(generator.convert(yxUser, YxUserQueryVo.class))
                .orderKey(cacheKey)
                .build();

    }

    /**
     * 根据地址信息
     * (重新)计算订单价格
     *
     * @param userInfo          用户
     * @param orderInfoCacheKey 订单缓存key
     * @return ComputeVo
     */
    @Override
    public ComputeVo computedOrder(YxUser userInfo, String orderInfoCacheKey, Long addressId) {
        Long uid = userInfo.getUid();
        OrderInfoCacheDto orderInfoCacheDTO = this.getCacheOrderInfo(uid, orderInfoCacheKey);

        if (ObjectUtil.isNull(orderInfoCacheDTO)) {
            throw new YshopException("订单已过期,请刷新当前页面");
        }

        BigDecimal payPrice = orderInfoCacheDTO.getPriceGroup().getTotalPrice();

        // 处理运费模板
        YxUserAddress userAddress = null;
        if (addressId != null && addressId != 0) {
            userAddress = userAddressService.getById(addressId);
        }

        List<YxStoreCartQueryVo> cartInfo = orderInfoCacheDTO.getCartInfo();
        if (cartInfo.size() == 0) {
            throw new YshopException("购买的商品为空");
        }

        PriceGroupDto priceGroup = this.getOrderPriceGroup(cartInfo, userAddress);
        BigDecimal payPostage = priceGroup.getStorePostage();

        payPrice = NumberUtil.add(payPrice, payPostage);

        // 获取商品仓库信息，并检测，不在同一个仓库不能下单
        // List<YxStoreCartQueryVo> cartInfo = cacheDTO.getCartInfo();
        // for (YxStoreCartQueryVo cart : cartInfo) {
        // }

        return ComputeVo.builder()
                .totalPrice(orderInfoCacheDTO.getPriceGroup().getTotalPrice())
                .totalInsurePrice(orderInfoCacheDTO.getPriceGroup().getTotalInsurePrice())
                .payPrice(payPrice)
                .payPostage(payPostage)
                .build();
    }

    /**
     * 创建订单
     *
     * @param userInfo          用户信息
     * @param orderInfoCacheKey key
     * @param orderCreateParam  param
     * @return YxStoreOrder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(key = ShopConstants.YSHOP_REDIS_INDEX_KEY, allEntries = true)
    public Order createOrder(YxUser userInfo, String orderInfoCacheKey, OrderParam orderCreateParam) {

        Long uid = userInfo.getUid();

        YxUser dbUser = userService.getById(uid);
        if (dbUser.getStatus() == YxUser.STATUS_BANED) {
            throw new YshopException("您的账户已被禁用");
        }

        // 0. 处理地址
        YxUserAddress userAddress = null;
        if (orderCreateParam.getAddressId() == null || orderCreateParam.getAddressId() == 0) {
            throw new YshopException("请选择收货地址");
        }

        Long addressID = orderCreateParam.getAddressId();

        userAddress = userAddressService.getById(addressID);
        if (ObjectUtil.isNull(userAddress)) {
            throw new YshopException("地址信息有误，请重新选择");
        }

        // log.info("创建订单：用户下单收货地址ID: {} {}", param.getAddressId(),
        // userAddress.getDetail());

        // 乐道：漯河市直接ban
        String city = userAddress.getCity();
        if (StringUtils.contains(city, "漯河市")) {
            throw new YshopException("您所在的地区暂不支持购买");
        }

        // 港澳台
        String province = userAddress.getProvince();
        String idCard = null;
        if (StringUtils.contains(province, "香港特别行政区") || StringUtils.contains(province, "澳门特别行政区") ||
                StringUtils.contains(province, "台湾省")) {
            idCard = userAddress.getIdCard();
        }

        String phone = userAddress.getPhone();
        boolean baned = userService.banedUser(phone);
        if (baned) {
            throw new YshopException("您的账户已被禁用");
        }

        Integer totalNum = 0;
        // 购物车ID
        List<String> cartIds = new ArrayList<>();

        // 确认订单时已经缓存
        OrderInfoCacheDto cacheDTO = this.getCacheOrderInfo(uid, orderInfoCacheKey);

        // 获取购物车信息
        List<YxStoreCartQueryVo> cartInfoList = cacheDTO.getCartInfo();
        if (cartInfoList.size() == 0) {
            throw new YshopException("购买的商品为空");
        }

        List<Long> lockedSkuIds = new ArrayList<>();
        // 按SKU ID升序锁定商品列表
        List<Long> skuIds = cartInfoList.stream()
                .map(YxStoreCartQueryVo::getProductInfo)
                .map(ProductQueryVo::getAttrInfo)
                .map(SKU::getId)
                .collect(Collectors.toList());
        skuIds.sort(Long::compareTo);

        String clientId = snowFlakeService.getMySnowFlake().nextIdStr();
        long startTime = System.currentTimeMillis();
        long startProcTime = System.currentTimeMillis();
        try {
            // !!!顺序加锁
            for (Long skuId : skuIds) {
                String lockKey = String.format(ShopConstants.LOCK_STOCK_SKU, skuId);
                if (!redisLock.tryLockWithWait(lockKey, clientId)) {
                    String msg = String.format("createOrder 获取锁失败: %s，客户端id: %s, 超时时间: %d", lockKey, clientId,
                            System.currentTimeMillis() - startTime);
                    log.error(msg);
                    throw new YshopException("系统繁忙，请稍后再试");
                }
                lockedSkuIds.add(skuId);
                log.info("createOrder 锁定商品: {}, clientId:{}", lockKey, clientId);
            }

            startProcTime = System.currentTimeMillis();

            // 查询每一件购物车中商品的库存
            for (YxStoreCartQueryVo cart : cartInfoList) {
                // 1. 检测库存
                yxStoreCartService.checkProductStock(uid, cart.getProductId(), cart.getCartNum(),
                        cart.getProductAttrUnique());

                // 2. 统计原始购物车的id
                cartIds.add(cart.getId().toString());
                // 3. 统计购买的所有商品数量
                totalNum += cart.getCartNum();
            }

            // 计算奖励积分
            BigDecimal gainIntegral = this.getGainIntegral(cartInfoList);

            // 生成分布式唯一值
            String orderSn = snowFlakeService.getMySnowFlake().nextIdStr();

            // 组合数据
            Order storeOrder = new Order();
            storeOrder.setUid(Long.valueOf(String.valueOf(uid)));
            storeOrder.setOrderId(orderSn);

            String realName = userAddress.getRealName();
            realName = EmojiParser.removeAllEmojis(realName);

            // 收货地址
            storeOrder.setIdCard(idCard);
            storeOrder.setRealName(realName);
            storeOrder.setCountryCode(userAddress.getCountryCode());
            storeOrder.setUserPhone(userAddress.getPhone());
            storeOrder.setUserAddress(userAddress.getDeliveryAddress());

            storeOrder.setCartId(StrUtil.join(",", cartIds));
            storeOrder.setTotalNum(totalNum);

            ComputeVo computeVo = this.computedOrder(userInfo, orderInfoCacheKey,
                    orderCreateParam.getAddressId());

            storeOrder.setTotalPrice(computeVo.getTotalPrice());
            storeOrder.setInsurePrice(computeVo.getTotalInsurePrice());
            storeOrder.setTotalPostage(computeVo.getPayPostage());

            storeOrder.setPayPrice(computeVo.getPayPrice());
            storeOrder.setPayPostage(computeVo.getPayPostage());
            storeOrder.setPaid(PayStsEnum.PAY_STATUS_0_UNPAY.getValue());
            storeOrder.setPayType(orderCreateParam.getPayType());

            storeOrder.setGainIntegral(gainIntegral);
            storeOrder.setMark(orderCreateParam.getMark());

            storeOrder.setIsChannel(ShopConstants.PAY_CHANNEL_WEAPP);
            storeOrder.setUnique(orderInfoCacheKey);

            boolean res = this.save(storeOrder);
            if (!res) {
                YshopException e = new YshopException("订单生成失败");
                e.setData(storeOrder);
                throw e;
            }

            // 减库存加销量
            this.deStockIncSale(storeOrder, cartInfoList);

            // 保存购物车商品信息
            orderCartInfoService.saveCartInfo(storeOrder.getId(), storeOrder.getOrderId(), cartInfoList);

            // 购物车状态修改
            YxStoreCart cartObj = new YxStoreCart();
            cartObj.setIsPay(PayStsEnum.PAY_STATUS_1_PAID.getValue());
            storeCartMapper.update(cartObj, Wrappers.<YxStoreCart>lambdaQuery()
                    .in(YxStoreCart::getId, cartIds));

            // 删除缓存
            this.delCacheOrderInfo(uid, orderInfoCacheKey);

            // 增加状态
            orderStatusService.create(storeOrder.getId(), OrderLogEnum.CREATE_ORDER.getValue(),
                    OrderLogEnum.CREATE_ORDER.getDesc());

            // 加入redis，到期自动取消
            // String redisKey = String.valueOf(StrUtil.format("{}{}",
            // ShopConstants.REDIS_ORDER_OUTTIME_UN_PAY, storeOrder.getId()));
            // redisTemplate.opsForValue().set(redisKey, storeOrder.getOrderId(),
            // ShopConstants.ORDER_OUTTIME_UN_PAY, TimeUnit.MINUTES);
            return storeOrder;
        } catch (Exception e) {
            log.error("创建订单失败: {}", e.getMessage());
            throw new YshopException(e.getMessage());
        } finally {
            Collections.reverse(lockedSkuIds);

            for (Long skuId : lockedSkuIds) {
                String lockKey = String.format(ShopConstants.LOCK_STOCK_SKU, skuId);
                int errorCode = redisLock.releaseLock(lockKey, clientId);
                if (errorCode == 1) {
                    log.warn("createOrder 锁已经释放，无需重复操作: {}, clientId:{}, errorCode:{}", lockKey, clientId,
                            errorCode);
                } else if (errorCode != 0) {
                    log.error("createOrder 释放锁失败: {}, clientId:{}, errorCode:{}", lockKey, clientId, errorCode);
                } else {
                    log.info("createOrder 释放锁成功: {}, clientId:{}", lockKey, clientId);
                }
            }

            long endProcTime = System.currentTimeMillis();
            log.info("createOrder 锁定耗时: {}ms, 执行耗时: {} ms", (startProcTime - startTime),
                    (endProcTime - startProcTime));
        }
    }

    /**
     * 确认订单退款
     *
     * @param orderId 单号
     * @param price   金额
     */
    @Override
    public void orderRefund(String orderId, BigDecimal price) {

        YxStoreOrderQueryVo orderQueryVo = getOrderInfo(orderId, null);
        if (ObjectUtil.isNull(orderQueryVo)) {
            throw new YshopException("订单不存在");
        }

        YxUserQueryVo userQueryVo = userService.getYxUserById(orderQueryVo.getUid());
        if (ObjectUtil.isNull(userQueryVo)) {
            throw new YshopException("用户不存在");
        }

        if (OrderRefundStatusEnum.REFUND_STATUS_2_DONE.equalsTo(orderQueryVo.getRefundStatus())) {
            throw new YshopException("订单已经退款了哦！");
        }

        // 0 找售后记录
        AfterSales afterSales = afterSalesService.lambdaQuery()
                .eq(AfterSales::getUserId, orderQueryVo.getUid())
                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                .eq(AfterSales::getOrderCode, orderQueryVo.getOrderId())
                .last("limit 1")
                .one();

        if (ObjectUtil.isNull(afterSales)) {
            // 退款必须来源于售后
            throw new YshopException("未找到相关的售后申请");
        }

        BigDecimal refundAmount = afterSales.getRefundAmount();
        if (price != null) {
            refundAmount = price;
        }

        if (orderQueryVo.getPayPrice().compareTo(refundAmount) < 0) {
            log.error("退款金额: {} 异常", refundAmount.toString());
            throw new YshopException("退款金额不正确");
        }

        Order storeOrder = new Order();
        storeOrder.setId(orderQueryVo.getId());

        // 1. 拒绝退款
        // if (ShopCommonEnum.REJECT_2.equalsTo(type)) {
        // // 1.1 退款状态改为初始态
        // storeOrder.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());
        // yxStoreOrderMapper.updateById(storeOrder);

        // // 1.2 获取售后信息
        // AfterSales afterSales = afterSalesService.lambdaQuery()
        // .eq(AfterSales::getUserId, orderQueryVo.getUid())
        // .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
        // .eq(AfterSales::getOrderCode, orderQueryVo.getOrderId()).one();

        // if (ObjectUtil.isNotNull(afterSales)) {
        // // 1.3 售后状态改为已拒绝
        // afterSalesService.lambdaUpdate()
        // .eq(AfterSales::getId, afterSales.getId())
        // .set(AfterSales::getSalesState, ShopCommonEnum.REJECT_2.getValue())
        // .update();
        // }
        // return;
        // }

        // 2.0 修改订单状态到退款

        // 2.1 记录状态
        orderStatusService.create(orderQueryVo.getId(), OrderLogEnum.REFUND_ORDER_SUCCESS.getValue(),
                "退款给用户：" + refundAmount.toString() + "元");

        // 2.2 创建微信退款事件
        // 为了解耦，利用spring boot事件机制
        TemplateBean templateBean = TemplateBean.builder()
                .orderId(orderQueryVo.getOrderId())
                .price(refundAmount.toString())
                .uid(orderQueryVo.getUid())
                .templateType(TemplateListenEnum.TYPE_2_REFUND_NOTIFY.getValue())
                .time(DateUtil.formatTime(new Date()))
                .payType(orderQueryVo.getPayType())
                .build();
        publisher.publishEvent(new TemplateEvent(this, templateBean));
    }

    /**
     * 修改收货地址
     * 已经有order的情况下，不应该用购买前的方式计算
     *
     * @param orderId
     * @param addressId
     */
    @Override
    public void changeAddress(String orderId, Long addressId) {
        YxStoreOrderQueryVo dbOrder = this.getOrderInfo(orderId, null);
        if (ObjectUtil.isNull(dbOrder)) {
            throw new YshopException("订单不存在");
        }

        boolean unPay = dbOrder.notPaid();

        int changeTimes = dbOrder.getAddressChangeCount() == null ? 0 : dbOrder.getAddressChangeCount();
        if (!unPay) {
            if (changeTimes > 0) {
                throw new YshopException("订单地址已经修改过一次，不可再次更改");
            }
        }

        /**
         * 旧的邮费
         */
        BigDecimal oldPostage = dbOrder.getPayPostage();

        YxUser user = userService.getById(dbOrder.getUid());

        // 2. 获取可用的购物车商品
        List<YxStoreCartQueryVo> itemList = new ArrayList<>();

        // 商品
        List<OrderCartItem> orderCartInfos = storeOrderCartInfoMapper.selectList(
                Wrappers.<OrderCartItem>lambdaQuery().eq(OrderCartItem::getOid, dbOrder.getId()));
        for (OrderCartItem item : orderCartInfos) {
            // 取出购物车信息
            YxStoreCartQueryVo cartInfo = item.cartInfoVO();
            itemList.add(cartInfo);
        }

        // 新地址
        YxUserAddress userAddress = userAddressService.getById(addressId);

        // 4. 获取新的邮费
        BigDecimal newPostage = this.handlePostage(itemList, userAddress);

        if (newPostage.compareTo(oldPostage) > 0) {
            log.error("邮费增加，不允许修改地址: aid: {}, address: {} {} {}", userAddress.getId(), userAddress.getProvince(),
                    userAddress.getCity(), userAddress.getDistrict());
            throw new YshopException("邮费增加，不允许修改地址");
        }

        // tips:修改地址，下调不退邮费
        Order order = new Order();
        order.setId(dbOrder.getId());

        String phone = userAddress.getPhone();
        boolean baned = userService.banedUser(phone);
        if (baned) {
            throw new YshopException("系统异常，请重试");
        }

        if (unPay) {
            // 未支付，可修改
            // 更新邮费支付
            order.setPayPostage(newPostage);
        } else {
            order.setAddressChangeCount(changeTimes + 1);
        }

        String realName = EmojiParser.removeAllEmojis(userAddress.getRealName());
        if (StrUtil.isBlank(realName)) {
            throw new YshopException("请填写姓名");
        }

        order.setRealName(realName);
        order.setCountryCode(userAddress.getCountryCode());
        order.setUserPhone(userAddress.getPhone());
        order.setUserAddress(userAddress.getDeliveryAddress());

        yxStoreOrderMapper.updateById(order);

        // 增加变更状态
        orderStatusService.create(order.getId(), OrderLogEnum.ORDER_EDIT_ADDRESS.getValue(), "修改订单地址");

        // TODO: 若已发货，需要通知快递公司修改地址，不支持
    }

    /**
     * 修改备注
     *
     * @param orderId
     * @param mark
     */
    @Override
    public void changeMark(String orderId, String mark) {
        YxStoreOrderQueryVo orderQueryVo = this.getOrderInfo(orderId, null);
        if (ObjectUtil.isNull(orderQueryVo)) {
            throw new YshopException("订单不存在");
        }
        Order storeOrder = Order.builder()
                .id(orderQueryVo.getId())
                .mark(mark)
                .build();

        yxStoreOrderMapper.updateById(storeOrder);

        // 增加变更状态
        orderStatusService.create(storeOrder.getId(), OrderLogEnum.ORDER_EDIT_MARK.getValue(), "修改订单备注");
    }

    /**
     * 订单发货
     *
     * @param orderId      单号
     * @param deliveryId   快递单号
     * @param deliveryName 快递公司名称(京东快递)
     * @param deliveryType 快递方式
     */
    @Override
    public void orderDelivery(String orderId, String deliveryId, String deliveryName, String deliveryType) {
        YxStoreOrderQueryVo orderQueryVo = this.getOrderInfo(orderId, null);
        if (ObjectUtil.isNull(orderQueryVo)) {
            throw new YshopException("订单不存在");
        }

        int status = orderQueryVo.getStatus();
        if (orderQueryVo.notPaid()) {
            throw new YshopException("订单未支付");
        }

        // 允许未发货以及出库中的订单发货
        int deliveryStatus = orderQueryVo.getDeliveryStatus();
        if (OrderInfoEnum.STATUS_NORMAL_0.equalsTo(status) || (OrderInfoEnum.STATUS_IN_DELIVERY_1.equalsTo(status)
                && OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.equalsTo(deliveryStatus))) {
        } else {
            log.error("发货失败，订单状态错误：orderId: {}, deliveryId: {}, status: {}, deliverySts: {}", orderId, deliveryId,
                    status, deliveryStatus);
            throw new YshopException("订单状态错误");
        }

        if (!OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.equalsTo(orderQueryVo.getRefundStatus())) {
            log.error("发货失败，订单退款中或已退款：orderId: {}, deliveryId: {}, status: {}, deliverySts: {}", orderId, deliveryId,
                    status, deliveryStatus);
            throw new YshopException("订单退款中或已退款");
        }

        Express expressCompany = expressService
                .getOne(new LambdaQueryWrapper<Express>().eq(Express::getName, deliveryName));
        if (ObjectUtil.isNull(expressCompany)) {
            log.error("快递公司不存在：{}", deliveryName);
            throw new YshopException("请后台先添加快递公司");
        }

        Order storeOrder = Order.builder()
                .id(orderQueryVo.getId())
                .status(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue())
                .deliveryId(deliveryId)
                .deliveryName(expressCompany.getName())
                .deliveryType(deliveryType)
                .deliverySn(expressCompany.getCode())
                .deliveryTime(new Date())
                .deliveryStatus(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue())
                .build();

        int succ = yxStoreOrderMapper.updateById(storeOrder);
        // log.info("订单发货：{}", succ == 1 ? true : false);

        // 增加状态
        orderStatusService.create(orderQueryVo.getId(), OrderLogEnum.DELIVERY_GOODS.getValue(),
                "已发货 快递公司：" + expressCompany.getName() + "快递单号：" + deliveryId);

        // 模板消息发布事件
        /*
         * 暂时不使用旧的模板消息机制发送发货信息
         *
         * TemplateBean templateBean = TemplateBean.builder()
         * .orderId(orderQueryVo.getOrderId())
         * .deliveryId(deliveryId)
         * .deliveryName(expressQueryVo.getName())
         * .uid(orderQueryVo.getUid())
         * .templateType(TemplateListenEnum.TYPE_3.getValue())
         * .build();
         * publisher.publishEvent(new TemplateEvent(this, templateBean));
         */

        // 加入redis，14天后自动确认收货
        // 不使用redis机制确认收货
        /*
         *
         * String redisKey = String.valueOf(StrUtil.format("{}{}",
         * ShopConstants.REDIS_ORDER_OUTTIME_UN_CONFIRM, orderQueryVo.getId()));
         * redisTemplate.opsForValue().set(redisKey, orderQueryVo.getOrderId(),
         * ShopConstants.ORDER_OUTTIME_UN_CONFIRM, TimeUnit.DAYS);
         */

    }

    /**
     * 修改快递单号
     *
     * @param orderId      单号
     * @param deliveryId   快递单号
     * @param deliveryName 快递公司code
     * @param deliveryType 快递方式
     */
    @Override
    public void updateDelivery(String orderId, String deliveryId, String deliveryName, String deliveryType) {
        YxStoreOrderQueryVo orderQueryVo = this.getOrderInfo(orderId, null);
        if (ObjectUtil.isNull(orderQueryVo)) {
            throw new YshopException("订单不存在");
        }

        boolean canSetDeliveryInfo = false;
        if (OrderInfoEnum.STATUS_NORMAL_0.equalsTo(orderQueryVo.getStatus())) {
            canSetDeliveryInfo = true;
        } else if (OrderInfoEnum.STATUS_IN_DELIVERY_1.equalsTo(orderQueryVo.getStatus())) {
            canSetDeliveryInfo = true;
        } else if (OrderInfoEnum.STATUS_AFTER_SALES.equalsTo(orderQueryVo.getStatus())) {
            canSetDeliveryInfo = false;
        } else if (OrderInfoEnum.STATUS_2_DONE.equalsTo(orderQueryVo.getStatus())) {
            canSetDeliveryInfo = false;
        }

        if (orderQueryVo.notPaid()) {
            canSetDeliveryInfo = false;
        }

        if (!canSetDeliveryInfo) {
            String msg = String.format("订单状态错误: %d", orderQueryVo.getStatus());
            throw new YshopException(msg);
        }

        Express expressQueryVo = expressService
                .getOne(new LambdaQueryWrapper<Express>().eq(Express::getName, deliveryName));
        if (ObjectUtil.isNull(expressQueryVo)) {
            throw new YshopException("请后台先添加快递公司");
        }

        Order storeOrder = Order.builder()
                .id(orderQueryVo.getId())
                .deliveryId(deliveryId)
                .deliveryName(expressQueryVo.getName())
                .deliveryType(deliveryType)
                .deliverySn(expressQueryVo.getCode())
                .deliveryTime(new Date())
                // 状态设置为已发货
                .status(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue())
                // 发货状态为已经联系快递
                .deliveryStatus(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue())
                .build();

        yxStoreOrderMapper.updateById(storeOrder);
    }

    /**
     * 修改订单价格
     *
     * @param orderId 单号
     * @param price   价格
     */
    @Override
    public void editOrderPrice(String orderId, String price) {
        YxStoreOrderQueryVo orderQueryVo = getOrderInfo(orderId, null);
        if (ObjectUtil.isNull(orderQueryVo)) {
            throw new YshopException("订单不存在");
        }

        // 价格不变不修改
        if (orderQueryVo.getPayPrice().compareTo(new BigDecimal(price)) == 0) {
            return;
        }

        // 已支付订单不可改价
        if (orderQueryVo.alreadyPaid()) {
            throw new YshopException("已付款订单不可改价");
        }

        Order storeOrder = new Order();
        storeOrder.setId(orderQueryVo.getId());
        storeOrder.setPayPrice(new BigDecimal(price));

        // 判断金额是否有变动,生成一个额外订单号去支付
        // 这里原因是支付渠道同一个order不能不同的价格
        // !!!所以订单号需要变化
        if (orderQueryVo.getPayPrice().compareTo(new BigDecimal(price)) != 0) {
            String orderSn = snowFlakeService.getMySnowFlake().nextIdStr();
            storeOrder.setExtendOrderId(orderSn);
        }

        yxStoreOrderMapper.updateById(storeOrder);

        // 增加变更状态
        orderStatusService.create(storeOrder.getId(), OrderLogEnum.ORDER_EDIT.getValue(), "修改实际支付金额");

    }

    /**
     * 锁定订单
     */
    @Override
    public String lockOrder(String orderId, String clientId) {
        String orderLockKey = String.format(ShopConstants.LOCK_KEY_ORDER_PAY_STATUS, orderId);
        if (!redisLock.tryLockWithWait(orderLockKey, clientId)) {
            throw new YshopException("订单正在处理中，请稍后再试");
        }
        return orderLockKey;
    }

    /**
     * 解锁订单
     */
    @Override
    public void unlockOrder(String orderId, String clientId) {
        String orderLockKey = String.format(ShopConstants.LOCK_KEY_ORDER_PAY_STATUS, orderId);
        redisLock.releaseLock(orderLockKey, clientId);
    }

    /**
     * 未付款取消订单
     *
     * @param orderId 订单号
     * @param uid     用户id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelOrder(String orderId, Long uid) {
        String clientId = snowFlakeService.getMySnowFlake().nextIdStr();
        String orderLockKey = null;
        try {
            // 1. 先获取订单锁
            orderLockKey = lockOrder(orderId, clientId);

            YxStoreOrderQueryVo order = this.getOrderInfo(orderId, uid);
            if (ObjectUtil.isNull(order)) {
                String msg = String.format("订单不存在,orderId=%s,uid=%d", orderId, uid);
                log.error(msg);
                throw new YshopException(msg);
            }

            if (order.alreadyPaid()) {
                throw new YshopException("订单已支付，不能取消");
            }

            if (order.getCancelStatus() == OrderInfoEnum.CANCEL_STATUS_2_CLOSED.getValue()) {
                log.error("已关闭订单不需要取消, orderId: {}", orderId);
                return;
            }

            if (order.getCancelStatus() == OrderInfoEnum.CANCEL_STATUS_1_CANCELED.getValue()) {
                log.error("已取消订单不用重复操作, orderId: {}", orderId);
                return;
            }

            // 这里不退积分，因为未付款
            String cartId = order.getCartId();
            LambdaQueryWrapper<OrderCartItem> query = new LambdaQueryWrapper<>();
            query.in(OrderCartItem::getCartId, Arrays.asList(cartId.split(",")));
            List<OrderCartItem> cartInfoList = orderCartInfoService.list(query);

            long startTime = System.currentTimeMillis();
            long startProcTime = System.currentTimeMillis();
            // 执行库存增减操作
            List<Long> lockedSkuIds = new ArrayList<>();
            // 按SKU ID升序锁定商品列表
            List<OrderCartItem> needRefundItems = new ArrayList<>();

            for (OrderCartItem cartInfo : cartInfoList) {
                needRefundItems.add(cartInfo);
            }
            List<Long> skuIds = needRefundItems.stream()
                    .map(OrderCartItem::cartInfoVO)
                    .map(YxStoreCartQueryVo::getProductInfo)
                    .map(ProductQueryVo::getAttrInfo)
                    .map(SKU::getId)
                    .collect(Collectors.toList());
            skuIds.sort(Long::compareTo);

            boolean waitLockFailed = false;

            try {
                // !!!顺序加锁
                for (Long skuId : skuIds) {
                    String lockKey = String.format(ShopConstants.LOCK_STOCK_SKU, skuId);
                    if (!redisLock.tryLockWithWait(lockKey, clientId)) {
                        waitLockFailed = true;
                        String msg = String.format("cancelOrder 获取锁失败: %s，客户端id: %s,order: %s 超时时间: %d ms", lockKey,
                                clientId,
                                order.getOrderId(),
                                System.currentTimeMillis() - startTime);
                        log.error(msg);
                        throw new YshopException("系统繁忙，请稍后再试");
                    }
                    lockedSkuIds.add(skuId);
                    log.info("cancelOrder 锁定商品: {}, clientId:{}, order: {}, 获取锁耗时: {} ms", lockKey, clientId,
                            order.getOrderId(),
                            System.currentTimeMillis() - startTime);
                }

                // 锁等到了，但是需要再次确认状态
                order = this.getOrderInfo(orderId, uid);
                if (order.alreadyPaid()) {
                    throw new YshopException("订单不在待付款状态，不能取消");
                }

                startProcTime = System.currentTimeMillis();

                this.regressionStock(order, cartInfoList, null);

                Order storeOrder = new Order();
                storeOrder.setCancelStatus(OrderInfoEnum.CANCEL_STATUS_1_CANCELED.getValue());
                storeOrder.setId(order.getId());
                yxStoreOrderMapper.updateById(storeOrder);
            } catch (Exception e) {
                log.error("取消订单失败: {}, {}", order.getOrderId(), e.getMessage());
                throw new YshopException("系统繁忙，请稍后再试");
            } finally {
                // 释放锁
                // !!!确保逆顺序释放锁
                Collections.reverse(lockedSkuIds);

                for (Long skuId : lockedSkuIds) {
                    String lockKey = String.format(ShopConstants.LOCK_STOCK_SKU, skuId);
                    int errorCode = redisLock.releaseLock(lockKey, clientId);
                    if (errorCode == 1) {
                        log.warn("cancelOrder 锁已经释放，无需重复操作: {}, clientId:{}, order: {}, errorCode:{}", lockKey,
                                clientId,
                                order.getOrderId(),
                                errorCode);
                    } else if (errorCode != 0) {
                        log.error("cancelOrder 释放锁失败: {}, clientId:{}, order: {}, errorCode:{}", lockKey, clientId,
                                order.getOrderId(),
                                errorCode);
                    } else {
                        log.info("cancelOrder 释放锁成功: {}, clientId:{}, order: {}", lockKey, clientId,
                                order.getOrderId());
                    }
                }
                long endProcTime = System.currentTimeMillis();
                if (waitLockFailed) {
                    log.info("cancelOrder {} 执行失败，等待锁耗时: {} ms", order.getOrderId(), (endProcTime - startTime));
                } else {
                    log.info("cancelOrder {} 锁定耗时: {}ms, 执行耗时: {} ms", order.getOrderId(), (startProcTime - startTime),
                            (endProcTime - startProcTime));
                }
            }

        } finally {
            if (orderLockKey != null) {
                unlockOrder(orderId, clientId);
            }
        }

    }

    /**
     * 未付款关闭订单
     *
     * @param orderId 订单号
     * @param uid     用户id
     */
    @Override
    @Transactional
    public void closeOrder(String orderId, Long uid) {
        String clientId = snowFlakeService.getMySnowFlake().nextIdStr();
        String orderLockKey = null;
        try {
            // 1. 先获取订单锁
            orderLockKey = lockOrder(orderId, clientId);

            YxStoreOrderQueryVo order = this.getOrderInfo(orderId, uid);
            if (ObjectUtil.isNull(order)) {
                throw new YshopException("订单不存在");
            }

            if (order.alreadyPaid()) {
                throw new YshopException("订单不在待付款状态，不能关闭");
            }

            if (order.getCancelStatus() == OrderInfoEnum.CANCEL_STATUS_1_CANCELED.getValue()) {
                log.error("已取消订单不可关闭, orderId: {}", orderId);
                return;
            }

            if (order.getCancelStatus() == OrderInfoEnum.CANCEL_STATUS_2_CLOSED.getValue()) {
                log.error("已关闭订单不可重复关闭, orderId: {}", orderId);
                return;
            }

            // 2024-0506 这里因为回归库存的锁导致在查询订单状态和查询库存的时候出现错误
            String cartId = order.getCartId();
            LambdaQueryWrapper<OrderCartItem> query = new LambdaQueryWrapper<>();
            query.in(OrderCartItem::getCartId, Arrays.asList(cartId.split(",")));
            List<OrderCartItem> cartInfoList = orderCartInfoService.list(query);

            long startTime = System.currentTimeMillis();
            long startProcTime = System.currentTimeMillis();
            // 执行库存增减操作
            List<Long> lockedSkuIds = new ArrayList<>();
            // 按SKU ID升序锁定商品列表
            List<OrderCartItem> needRefundItems = new ArrayList<>();

            for (OrderCartItem cartInfo : cartInfoList) {
                needRefundItems.add(cartInfo);
            }
            List<Long> skuIds = needRefundItems.stream()
                    .map(OrderCartItem::cartInfoVO)
                    .map(YxStoreCartQueryVo::getProductInfo)
                    .map(ProductQueryVo::getAttrInfo)
                    .map(SKU::getId)
                    .collect(Collectors.toList());
            skuIds.sort(Long::compareTo);

            boolean waitLockFailed = false;

            try {
                // !!!顺序加锁
                for (Long skuId : skuIds) {
                    String lockKey = String.format(ShopConstants.LOCK_STOCK_SKU, skuId);
                    if (!redisLock.tryLockWithWait(lockKey, clientId)) {
                        waitLockFailed = true;
                        String msg = String.format("closeOrder 获取锁失败: %s，客户端id: %s,order: %s 超时时间: %d ms", lockKey,
                                clientId,
                                order.getOrderId(),
                                System.currentTimeMillis() - startTime);
                        log.error(msg);
                        throw new YshopException("系统繁忙，请稍后再试");
                    }
                    lockedSkuIds.add(skuId);
                    log.info("closeOrder 锁定商品: {}, clientId:{}, order: {}, 获取锁耗时: {} ms", lockKey, clientId,
                            order.getOrderId(),
                            System.currentTimeMillis() - startTime);
                }

                // 锁等到了，但是需要再次确认状态
                order = this.getOrderInfo(orderId, uid);
                if (order.alreadyPaid()) {
                    throw new YshopException("订单不在待付款状态，不能关闭");
                }

                startProcTime = System.currentTimeMillis();
                this.regressionStock(order, cartInfoList, null);

                Order storeOrder = new Order();
                storeOrder.setCancelStatus(OrderInfoEnum.CANCEL_STATUS_2_CLOSED.getValue());
                storeOrder.setId(order.getId());
                yxStoreOrderMapper.updateById(storeOrder);
            } catch (Exception e) {
                log.error("关闭订单失败: {}, {}", order.getOrderId(), e.getMessage());
                throw new YshopException("系统繁忙，请稍后再试");
            } finally {
                // 释放锁
                // !!!确保逆顺序释放锁
                Collections.reverse(lockedSkuIds);

                for (Long skuId : lockedSkuIds) {
                    String lockKey = String.format(ShopConstants.LOCK_STOCK_SKU, skuId);
                    int errorCode = redisLock.releaseLock(lockKey, clientId);
                    if (errorCode == 1) {
                        log.warn("closeOrder 锁已经释放，无需重复操作: {}, clientId:{}, order: {}, errorCode:{}", lockKey, clientId,
                                order.getOrderId(),
                                errorCode);
                    } else if (errorCode != 0) {
                        log.error("closeOrder 释放锁失败: {}, clientId:{}, order: {}, errorCode:{}", lockKey, clientId,
                                order.getOrderId(),
                                errorCode);
                    } else {
                        log.info("closeOrder 释放锁成功: {}, clientId:{}, order: {}", lockKey, clientId, order.getOrderId());
                    }
                }
                long endProcTime = System.currentTimeMillis();
                if (waitLockFailed) {
                    log.info("closeOrder {} 执行失败，等待锁耗时: {} ms", order.getOrderId(), (endProcTime - startTime));
                } else {
                    log.info("closeOrder {} 锁定耗时: {}ms, 执行耗时: {} ms", order.getOrderId(), (startProcTime - startTime),
                            (endProcTime - startProcTime));
                }
            }
        } finally {
            if (orderLockKey != null) {
                unlockOrder(orderId, clientId);
            }
        }
    }

    /**
     * 设置为出库中状态
     * 从待发货到出库中
     *
     * @param orderId
     * @param uid
     */
    @Override
    public void setDeliverOfOutDepot(String orderId, Long uid) {
        YxStoreOrderQueryVo order = this.getOrderInfo(orderId, uid);
        if (ObjectUtil.isNull(order)) {
            throw new YshopException("订单不存在");
        }

        if (order.getDeliveryStatus() == OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue()) {
            return;
        }

        if (order.getDeliveryStatus() == OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue()) {
            // 已经都发货了，不能倒回去
            return;
        }

        // if (order.getStatus() != ClientOrderStatusEnum.STATUS_UNPAY_0.getValue()) {
        // throw new YshopException("订单不在待付款状态，不能关闭");
        // }

        Order storeOrder = new Order();
        storeOrder.setStatus(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
        storeOrder.setDeliveryStatus(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue());
        storeOrder.setId(order.getId());
        yxStoreOrderMapper.updateById(storeOrder);
    }

    /**
     * 删除订单
     *
     * @param orderId 单号
     * @param uid     uid
     */
    @Override
    public void removeOrder(String orderId, Long uid) {
        YxStoreOrderQueryVo order = getOrderInfo(orderId, (long) uid);
        if (order == null) {
            throw new YshopException("订单不存在");
        }
        order = mashupOrderCartAndStatus(order);

        if (order.alreadyPaid()) {
            throw new YshopException("已支付的订单不能删除");
        } else if (OrderInfoEnum.STATUS_2_DONE.equalsTo(order.getStatus())) {
            throw new YshopException("已完成的订单不能删除");
        }

        yxStoreOrderMapper.deleteById(order.getId());

        // 增加状态
        orderStatusService.create(order.getId(),
                OrderLogEnum.REMOVE_ORDER.getValue(),
                OrderLogEnum.REMOVE_ORDER.getDesc());
    }

    /**
     * 订单确认收货
     *
     * @param orderId 单号
     * @param uid     uid
     */
    @CacheEvict(key = ShopConstants.YSHOP_REDIS_INDEX_KEY, allEntries = true)
    @Override
    public void takeOrder(String orderId, Long uid) {
        YxStoreOrderQueryVo order = this.getOrderInfo(orderId, uid);
        if (ObjectUtil.isNull(order)) {
            throw new YshopException("订单不存在");
        }

        order = mashupOrderCartAndStatus(order);

        if (OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue() != order.getStatus()) {
            if (OrderInfoEnum.STATUS_2_DONE.getValue() == order.getStatus()) {
                // 订单已完成，不能重复确认收货
            } else {
                log.info("订单非待收货状态，不能确认收货, orderId: {}, status: {}", orderId, order.getStatus());
            }
            return;
        }

        Order storeOrder = new Order();
        storeOrder.setStatus(OrderInfoEnum.STATUS_2_DONE.getValue());
        storeOrder.setId(order.getId());
        storeOrder.setFinishTime(new Date());
        yxStoreOrderMapper.updateById(storeOrder);

        // 增加状态
        orderStatusService.create(order.getId(), OrderLogEnum.TAKE_ORDER_DELIVERY.getValue(),
                OrderLogEnum.TAKE_ORDER_DELIVERY.getDesc());

        // 奖励积分
        this.gainUserIntegral(order);

        // 分销计算
        // userService.backOrderBrokerage(order);

        // 检查是否符合会员升级条件
        // userLevelService.setLevelComplete(uid);
    }

    /**
     * 微信订单确认收货
     *
     * @param orderId 单号
     * @param uid     uid
     */
    @CacheEvict(key = ShopConstants.YSHOP_REDIS_INDEX_KEY, allEntries = true)
    @Override
    public boolean wechatTakeOrder(String orderId, Long uid) {
        YxStoreOrderQueryVo order = this.getOrderInfo(orderId, uid);
        if (ObjectUtil.isNull(order)) {
            log.error("订单不存在: {}", orderId);
            throw new YshopException("订单不存在");
        }

        if (StringUtils.isBlank(order.getDeliveryId())) {
            log.error("订单未发货，不能确认收货: {}", orderId);
            throw new YshopException("订单尚未发货，不能确认收货");
        }

        // 本系统标记确认收货
        takeOrder(orderId, uid);

        String outTradeNo = order.getOutTradeNo();
        JSONObject wechatOrder = weiXinOrderShippingService.queryOrderShippingStatus(null, outTradeNo);
        if (wechatOrder == null) {
            log.error("微信订单确认收货，微信订单状态查询异常，订单号: {}", outTradeNo);
            return false;
        }

        // (0) 初始状态 (1) 待发货；(2) 已发货；(3) 确认收货；(4) 交易完成；(5) 已退款。
        int orderState = wechatOrder.getIntValue("order_state"); // Get order status as an integer
        String stateCN = WeiXinOrderShippingService.OrderState.cn(orderState);

        log.info("物流状态查询，订单号: {}, 微信物流状态: {} {}", outTradeNo, orderState, stateCN);

        if (orderState == WeiXinOrderShippingService.OrderState.CONFIRMED_RECEIPT.getCode() ||
                orderState == WeiXinOrderShippingService.OrderState.TRADE_COMPLETED.getCode()) {
            if (order.getWechatOrderStatus() != orderState) {
                // 已发货状态，标记确认收货
                Order storeOrder = new Order();
                storeOrder.setId(order.getId());
                storeOrder.setWechatOrderStatus(orderState);
                storeOrder.setFinishTime(new Date());
                yxStoreOrderMapper.updateById(storeOrder);
                log.info("微信订单确认收货，订单号: {}，微信物流状态更新: {} {}", outTradeNo, orderState, stateCN);
                return true;
            }
        } else {
            log.warn("微信订单确认收货，订单号: {}, 微信物流状态异常: {} {}", outTradeNo, orderState, stateCN);
        }
        // else if (orderState ==
        // WeiXinOrderShippingService.OrderState.REFUNDED.getCode()) {
        // log.error("微信订单已退款，订单号: {}", outTradeNo);
        // if (order.getWechatOrderStatus() != orderState) {
        // Order storeOrder = new Order();
        // storeOrder.setId(order.getId());
        // storeOrder.setWechatOrderStatus(orderState);
        // yxStoreOrderMapper.updateById(storeOrder);
        // }
        // return false;
        // }
        return true;
    }

    /**
     * 核销订单
     *
     * @param verifyCode 核销码
     * @param isConfirm  OrderInfoEnum
     * @param uid        uid
     * @return YxStoreOrderQueryVo
     */
    // @Override
    // public YxStoreOrderQueryVo verifyOrder(String verifyCode, Integer isConfirm,
    // Long uid) {

    // Order order = this.getOne(Wrappers.<Order>lambdaQuery()
    // .eq(Order::getVerifyCode, verifyCode)
    // .eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
    // .eq(Order::getRefundStatus,
    // OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue()));
    // if (order == null) {
    // throw new YshopException("核销的订单不存在或未支付或已退款");
    // }

    // // if (uid != null) {
    // // boolean checkStatus = systemStoreStaffService.checkStatus(uid,
    // // order.getStoreId());
    // // if (!checkStatus) {
    // // throw new YshopException("您没有当前店铺核销权限");
    // // }
    // // }

    // if (!OrderInfoEnum.STATUS_NORMAL_0.equalsTo(order.getStatus())) {
    // throw new YshopException("订单已经核销");
    // }

    // YxStoreOrderQueryVo orderQueryVo = generator.convert(order,
    // YxStoreOrderQueryVo.class);
    // if (OrderInfoEnum.CONFIRM_STATUS_0.equalsTo(isConfirm)) {
    // return orderQueryVo;
    // }

    // Order storeOrder = new Order();
    // storeOrder.setStatus(OrderInfoEnum.STATUS_2_DONE.getValue());
    // storeOrder.setId(order.getId());
    // yxStoreOrderMapper.updateById(storeOrder);

    // // 增加状态
    // orderStatusService.create(order.getId(),
    // OrderLogEnum.TAKE_ORDER_DELIVERY.getValue(), "已核销");

    // // 奖励积分
    // this.gainUserIntegral(orderQueryVo);

    // // 分销计算
    // userService.backOrderBrokerage(orderQueryVo);

    // // 检查是否符合会员升级条件
    // userLevelService.setLevelComplete(order.getUid());

    // return null;
    // }

    /**
     * 申请退款
     *
     * @param explain 退款备注
     * @param Img     图片
     * @param text    理由
     * @param orderId 订单号
     * @param uid     uid
     */
    @Override
    public void orderApplyRefund(String explain, String Img, String text, String orderId, Long uid) {
        YxStoreOrderQueryVo order = getOrderInfo(orderId, uid);
        if (order == null) {
            throw new YshopException("订单不存在");
        }

        if (OrderRefundStatusEnum.REFUND_STATUS_2_DONE.equalsTo(order.getRefundStatus())) {
            throw new YshopException("订单已退款");
        }
        if (OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.equalsTo(order.getRefundStatus())) {
            throw new YshopException("正在申请退款中");
        }
        if (OrderInfoEnum.STATUS_IN_DELIVERY_1.equalsTo(order.getStatus())) {
            throw new YshopException("订单当前无法退款");
        }

        Order storeOrder = new Order();
        storeOrder.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue());
        storeOrder.setRefundReasonTime(new Date());
        storeOrder.setRefundReasonWapExplain(explain);
        storeOrder.setRefundReasonWapImg(Img);
        storeOrder.setRefundReasonWap(text);
        storeOrder.setId(order.getId());
        yxStoreOrderMapper.updateById(storeOrder);

        // 增加状态
        orderStatusService.create(order.getId(),
                OrderLogEnum.REFUND_ORDER_APPLY.getValue(),
                "用户申请退款，原因：" + text);

        // 模板消息发布事件
        TemplateBean templateBean = TemplateBean.builder()
                .orderId(order.getOrderId())
                .price(order.getPayPrice().toString())
                .uid(order.getUid())
                .templateType(TemplateListenEnum.TYPE_9.getValue())
                .time(DateUtil.formatTime(new Date()))
                .build();
        publisher.publishEvent(new TemplateEvent(this, templateBean));

    }

    /**
     * 订单列表
     *
     * @param uid   用户id
     * @param type  ClientOrderStatusEnum
     * @param page  page
     * @param limit limit
     * @return list
     */
    @Override
    public Map<String, Object> orderList(Long uid, int type, int page, int limit) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        if (uid != null) {
            wrapper.eq(Order::getUid, uid);
        }
        wrapper.orderByDesc(Order::getId);

        switch (ClientOrderStatusEnum.toType(type)) {
            case STATUS__1:
                break;
            // 未支付
            case STATUS_UNPAY_0:
                wrapper.eq(Order::getPaid, PayStsEnum.PAY_STATUS_0_UNPAY.getValue())
                        .eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue())
                        .eq(Order::getCancelStatus, OrderInfoEnum.CANCEL_STATUS_0_NORMAL.getValue())
                        .eq(Order::getStatus, OrderInfoEnum.STATUS_NORMAL_0.getValue());
                break;
            // 待收货
            case STATUS_WAIT_RECEIVE:
                wrapper.eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                        .eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue())
                        .in(Order::getStatus, OrderInfoEnum.STATUS_NORMAL_0.getValue(),
                                OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
                break;
            // 已完成
            case STATUS_FINISH:
                wrapper.eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                        .eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue())
                        .in(Order::getStatus, OrderInfoEnum.STATUS_2_DONE.getValue(),
                                OrderInfoEnum.STATUS_2_DONE.getValue());
                break;
            // 退换货
            case STATUS_CUSTOM_SERVICE:
                wrapper.eq(Order::getStatus, OrderInfoEnum.STATUS_AFTER_SALES.getValue());
                // wrapper.or(i -> i.eq(YxStoreOrder::getPaid,
                // PayStsEnum.PAY_STATUS_1_PAID.getValue())
                // .eq(YxStoreOrder::getRefundStatus,
                // OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue()))
                // .or(i -> i.eq(YxStoreOrder::getPaid,
                // PayStsEnum.PAY_STATUS_0_UNPAY.getValue())
                // .eq(YxStoreOrder::getRefundStatus,
                // OrderRefundStatusEnum.REFUND_STATUS_2_DONE.getValue()))
                // .or(i -> {
                // String[] refundStatus = { "1", "2" };
                // i.eq(YxStoreOrder::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                // .in(YxStoreOrder::getRefundStatus, Arrays.asList(refundStatus));
                // });
                break;
            default:
        }

        Page<Order> pageModel = new Page<>(page, limit);
        IPage<Order> pageList = yxStoreOrderMapper.selectPage(pageModel, wrapper);
        List<YxStoreOrderQueryVo> list = generator.convert(pageList.getRecords(), YxStoreOrderQueryVo.class);

        // 补充售后信息
        list = list.stream().map(o -> {
            // 售后订单
            if (OrderInfoEnum.STATUS_AFTER_SALES.equalsTo(o.getStatus())) {
                LambdaQueryWrapper<AfterSales> asWrapper = new LambdaQueryWrapper<>();
                asWrapper.eq(AfterSales::getOrderCode, o.getOrderId())
                        .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(asWrapper);
                if (Objects.nonNull(afterSales)) {
                    AfterSalesVo afterSalesVo = generator.convert(afterSales, AfterSalesVo.class);
                    o.setAfterSales(afterSalesVo);
                }
            }

            return o;
        }).collect(Collectors.toList());

        Map<String, Object> map = new HashMap<>();
        map.put("list", list.stream()
                .map(this::mashupOrderCartAndStatus)
                .collect(Collectors.toList()));
        map.put("total", pageList.getTotal());
        map.put("totalPage", pageList.getPages());
        return map;

    }

    /**
     * 获取 今日 昨日 本月 订单金额
     *
     * @return ShoperOrderTimeDataVo
     */
    @Override
    public ShoperOrderTimeDataVo getShoperOrderTimeData() {

        Date today = DateUtil.beginOfDay(new Date());
        Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
        Date nowMonth = DateUtil.beginOfMonth(new Date());
        Date lastWeek = DateUtil.beginOfDay(DateUtil.lastWeek());

        ShoperOrderTimeDataVo orderTimeDataVo = new ShoperOrderTimeDataVo();

        // 今日成交额
        LambdaQueryWrapper<Order> wrapperOne = new LambdaQueryWrapper<>();
        wrapperOne
                .ge(Order::getPayTime, today)
                .eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                .eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());
        // orderTimeDataVo.setTodayPrice(yxStoreOrderMapper.todayPrice(wrapperOne));
        // 今日订单数
        orderTimeDataVo.setTodayCount(yxStoreOrderMapper.selectCount(wrapperOne));

        // 昨日成交额
        LambdaQueryWrapper<Order> wrapperTwo = new LambdaQueryWrapper<>();
        wrapperTwo
                .lt(Order::getPayTime, today)
                .ge(Order::getPayTime, yesterday)
                .eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                .eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());
        // orderTimeDataVo.setProPrice(yxStoreOrderMapper.todayPrice(wrapperTwo));
        // 昨日订单数
        orderTimeDataVo.setProCount(yxStoreOrderMapper.selectCount(wrapperTwo));

        // 本月成交额
        LambdaQueryWrapper<Order> wrapperThree = new LambdaQueryWrapper<>();
        wrapperThree
                .ge(Order::getPayTime, nowMonth)
                .eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                .eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());
        orderTimeDataVo.setMonthPrice(yxStoreOrderMapper.todayPrice(wrapperThree));
        // 本月订单数
        orderTimeDataVo.setMonthCount(yxStoreOrderMapper.selectCount(wrapperThree));

        // 上周统计
        LambdaQueryWrapper<Order> wrapperLastWeek = new LambdaQueryWrapper<>();
        wrapperLastWeek
                .lt(Order::getPayTime, today)
                .ge(Order::getPayTime, lastWeek)
                .eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                .eq(Order::getRefundStatus,
                        OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());
        // orderTimeDataVo.setLastWeekPrice(yxStoreOrderMapper.todayPrice(wrapperLastWeek));

        // 上周订单数
        orderTimeDataVo.setLastWeekCount(yxStoreOrderMapper.selectCount(wrapperLastWeek));

        // 待发货数量(1.5)
        YxStoreOrderQueryCriteria criteria = new YxStoreOrderQueryCriteria();
        criteria.setPaid(PayStsEnum.PAY_STATUS_1_PAID.getValue());
        criteria.setStatus(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
        criteria.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());
        criteria.setDeliveryStatus(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue());

        PageResult<YxStoreOrderDto> result = queryPagedResult(criteria, Pageable.ofSize(1).withPage(0));
        orderTimeDataVo.setPendingShippingCount(result.getTotalElements());

        // 已发货数量(2)
        criteria = new YxStoreOrderQueryCriteria();
        criteria.setPaid(PayStsEnum.PAY_STATUS_1_PAID.getValue());
        criteria.setStatus(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
        criteria.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());
        criteria.setDeliveryStatus(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue());

        result = queryPagedResult(criteria, Pageable.ofSize(1).withPage(0));
        orderTimeDataVo.setInShippingCount(result.getTotalElements());

        // 待处理售后数量
        AfterSalesQueryCriteria afterSalesQueryCriteria = new AfterSalesQueryCriteria();
        afterSalesQueryCriteria.setState(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue());
        afterSalesQueryCriteria.setSalesState(AfterSales.SALES_CANCEL_STATUS_NORMAL);

        PageResult<AfterSalesVo> afterSalesResult = afterSalesService.queryAll(afterSalesQueryCriteria,
                Pageable.ofSize(1).withPage(0), false);
        orderTimeDataVo.setPendingAfterSalesCount(afterSalesResult.getTotalElements());

        // 待退款
        afterSalesQueryCriteria = new AfterSalesQueryCriteria();
        afterSalesQueryCriteria.setState(AfterSalesStateEnum.STATUS_USER_SENT_OR_WAIT_REFUND_2.getValue());
        afterSalesQueryCriteria.setSalesState(AfterSales.SALES_CANCEL_STATUS_NORMAL);

        afterSalesResult = afterSalesService.queryAll(afterSalesQueryCriteria,
                Pageable.ofSize(1).withPage(0), false);
        orderTimeDataVo.setWaitRefundAfterSalesCount(afterSalesResult.getTotalElements());

        return orderTimeDataVo;
    }

    /**
     * 订单每月统计数据
     *
     * @param page  page
     * @param limit list
     * @return List
     */
    @Override
    public List<OrderDataVo> getOrderDataPriceCount(int page, int limit) {
        Page<Order> pageModel = new Page<>(page, limit);
        return yxStoreOrderMapper.getOrderDataPriceList(pageModel);
    }

    /**
     * 获取某个用户的订单统计数据
     *
     * @param uid uid>0 取用户 否则取所有
     * @return UserOrderCountVo
     */
    @Override
    public UserOrderCountVo orderData(Long uid) {

        // 订单支付没有退款 数量
        LambdaQueryWrapper<Order> wrapperOne = new LambdaQueryWrapper<>();
        if (uid != null) {
            wrapperOne.eq(Order::getUid, uid);
        }
        wrapperOne.eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue())
                .eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue());
        Long orderCount = yxStoreOrderMapper.selectCount(wrapperOne);

        // 订单支付没有退款 支付总金额
        double sumPrice = yxStoreOrderMapper.sumPrice(uid);

        // 订单待支付 数量
        LambdaQueryWrapper<Order> wrapperTwo = new LambdaQueryWrapper<>();
        if (uid != null) {
            wrapperTwo.eq(Order::getUid, uid);
        }
        wrapperTwo.eq(Order::getPaid, PayStsEnum.PAY_STATUS_0_UNPAY.getValue())
                .eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue())
                .eq(Order::getCancelStatus, OrderInfoEnum.CANCEL_STATUS_0_NORMAL.getValue())
                .eq(Order::getStatus, OrderInfoEnum.STATUS_NORMAL_0.getValue());
        Long unpaidCount = yxStoreOrderMapper.selectCount(wrapperTwo);

        // 订单待发货 数量
        LambdaQueryWrapper<Order> wrapperThree = new LambdaQueryWrapper<>();
        if (uid != null) {
            wrapperThree.eq(Order::getUid, uid);
        }
        wrapperThree.eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                .eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue())
                .eq(Order::getStatus, OrderInfoEnum.STATUS_NORMAL_0.getValue());
        Long unshippedCount = yxStoreOrderMapper.selectCount(wrapperThree);

        // 订单待收货 数量
        LambdaQueryWrapper<Order> wrapperFour = new LambdaQueryWrapper<>();
        if (uid != null) {
            wrapperFour.eq(Order::getUid, uid);
        }
        // wrapperFour.eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
        // .eq(Order::getRefundStatus,
        // OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue())
        // .eq(Order::getStatus, OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
        wrapperFour.eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                .eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue())
                .in(Order::getStatus, OrderInfoEnum.STATUS_NORMAL_0.getValue(),
                        OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
        Long receivedCount = yxStoreOrderMapper.selectCount(wrapperFour);

        // 订单待评价 数量
        LambdaQueryWrapper<Order> wrapperFive = new LambdaQueryWrapper<>();
        if (uid != null) {
            wrapperFive.eq(Order::getUid, uid);
        }
        wrapperFive.eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                .eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue())
                .eq(Order::getStatus, OrderInfoEnum.STATUS_2_DONE.getValue());
        Long evaluatedCount = yxStoreOrderMapper.selectCount(wrapperFive);

        // 订单已完成 数量
        LambdaQueryWrapper<Order> wrapperSix = new LambdaQueryWrapper<>();
        if (uid != null) {
            wrapperSix.eq(Order::getUid, uid);
        }
        wrapperSix.eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                .eq(Order::getRefundStatus, OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue())
                .eq(Order::getStatus, OrderInfoEnum.STATUS_2_DONE.getValue());
        Long completeCount = yxStoreOrderMapper.selectCount(wrapperSix);

        // 订单退款
        LambdaQueryWrapper<Order> wrapperSeven = new LambdaQueryWrapper<>();
        if (uid != null) {
            wrapperSeven.eq(Order::getUid, uid);
        }
        String[] strArr = { "1", "2" };
        wrapperSeven.eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                .in(Order::getRefundStatus, Arrays.asList(strArr));
        Long refundCount = yxStoreOrderMapper.selectCount(wrapperSeven);

        return UserOrderCountVo.builder()
                .orderCount(orderCount)
                .sumPrice(sumPrice)
                // 待付款
                .unpaidCount(unpaidCount)
                .unshippedCount(unshippedCount)
                // 新待收货
                .receivedCount(receivedCount)
                .evaluatedCount(evaluatedCount)
                .completeCount(completeCount)
                .refundCount(refundCount)
                .build();
    }

    /**
     * 混入购物车信息
     */
    private YxStoreOrderQueryVo mashupOrderCart(YxStoreOrderQueryVo order) {
        // 1. 获取购物车
        LambdaQueryWrapper<OrderCartItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderCartItem::getOid, order.getId());
        List<OrderCartItem> cartInfos = orderCartInfoService.list(wrapper);

        List<YxStoreCartQueryVo> cartInfo = cartInfos.stream()
                .map(cart -> {
                    YxStoreCartQueryVo cartQueryVo = JSON.parseObject(cart.getCartInfo(), YxStoreCartQueryVo.class);
                    cartQueryVo.setUnique(cart.getUnique());
                    return cartQueryVo;
                })
                .collect(Collectors.toList());
        order.setCartInfo(cartInfo);

        return order;
    }

    /**
     * 处理订单返回的状态
     *
     * @param order order
     * @return YxStoreOrderQueryVo
     */
    @Override
    public YxStoreOrderQueryVo mashupOrderCartAndStatus(YxStoreOrderQueryVo order) {
        // 1. 混入购物车
        order = mashupOrderCart(order);

        // 2. 获取状态变化
        StatusDto statusDTO = new StatusDto();
        if (order.notPaid()) {
            int cancelStatus = order.getCancelStatus();
            if (cancelStatus == OrderInfoEnum.CANCEL_STATUS_0_NORMAL.getValue()) {
                // 计算未支付到自动取消订 时间
                int offset = Integer.valueOf(String.valueOf(ShopConstants.ORDER_OUTTIME_UN_PAY));
                Date time = DateUtil.offsetMinute(order.getCreateTime(), offset);
                statusDTO.set_class("nobuy");
                statusDTO.set_msg(StrUtil.format("请在{}前完成支付", DateUtil.formatDateTime(time)));
                statusDTO.set_type("0");
                statusDTO.set_title("未支付");
            } else if (cancelStatus == OrderInfoEnum.CANCEL_STATUS_1_CANCELED.getValue()) {
                statusDTO.set_class("nobuy");
                statusDTO.set_msg("订单已取消");
                statusDTO.set_type("0");
                statusDTO.set_title("已取消");
            } else if (cancelStatus == OrderInfoEnum.CANCEL_STATUS_2_CLOSED.getValue()) {
                statusDTO.set_class("nobuy");
                statusDTO.set_msg("订单已关闭");
                statusDTO.set_type("0");
                statusDTO.set_title("已关闭");
            }
        } else if (OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.equalsTo(order.getRefundStatus())) {
            statusDTO.set_class("state-sqtk");
            statusDTO.set_msg("商家审核中,请耐心等待");
            statusDTO.set_type("-1");
            statusDTO.set_title("申请退款中");
        } else if (OrderRefundStatusEnum.REFUND_STATUS_2_DONE.equalsTo(order.getRefundStatus())) {
            statusDTO.set_class("state-sqtk");
            statusDTO.set_msg("已为您退款,感谢您的支持");
            statusDTO.set_type("-2");
            statusDTO.set_title("已退款");
        } else if (OrderInfoEnum.STATUS_NORMAL_0.equalsTo(order.getStatus())) {

            // if
            // (OrderInfoEnum.SHIPPIING_TYPE_1.equalsTo(order.getShippingType())) {
            statusDTO.set_class("state-nfh");
            statusDTO.set_msg("商家未发货,请耐心等待");
            statusDTO.set_type("1");
            statusDTO.set_title("未发货");
            // } else {
            // statusDTO.set_class("state-nfh");
            // statusDTO.set_msg("待核销,请到核销点进行核销");
            // statusDTO.set_type("1");
            // statusDTO.set_title("待核销");
            // }

        } else if (OrderInfoEnum.STATUS_IN_DELIVERY_1.equalsTo(order.getStatus())) {
            statusDTO.set_class("state-ysh");
            statusDTO.set_msg("服务商已发货");
            statusDTO.set_type("2");

            if (OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.equalsTo(order.getDeliveryStatus())) {
                statusDTO.set_title("出库中");
            } else {
                statusDTO.set_title("已发货");
            }
        } else if (OrderInfoEnum.STATUS_2_DONE.equalsTo(order.getStatus())) {
            statusDTO.set_class("state-ytk");
            statusDTO.set_msg("交易完成,感谢您的支持");
            statusDTO.set_type("4");
            statusDTO.set_title("交易完成");
        }

        if (PayTypeEnum.WEIXIN.equalsTo(order.getPayType())) {
            statusDTO.set_payType("微信支付");
            // } else if (PayTypeEnum.YUE.equalsTo(order.getPayType())) {
            // statusDTO.set_payType("余额支付");
        } else {
            statusDTO.set_payType("积分支付");
        }

        order.set_status(statusDTO);

        return order;
    }

    /**
     * 支付成功后操作
     *
     * @param orderId 订单号
     * @param payType 支付方式
     */
    @Override
    public void paySuccess(String orderId, String payType) {
        String clientId = snowFlakeService.getMySnowFlake().nextIdStr();
        String orderLockKey = null;
        try {
            // 1. 先获取订单锁
            orderLockKey = lockOrder(orderId, clientId);

            YxStoreOrderQueryVo orderInfo = getOrderInfo(orderId, null);

            if (orderInfo == null) {
                log.error("订单不存在，orderId: {}", orderId);
                return;
            }

            if (orderInfo.alreadyPaid()) {
                log.error("订单已支付，orderId: {}", orderId);
                return;
            }

            // 更新订单状态
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Order::getOrderId, orderId);
            Order storeOrder = new Order();
            storeOrder.setPaid(PayStsEnum.PAY_STATUS_1_PAID.getValue());
            storeOrder.setPayType(payType);
            storeOrder.setPayTime(new Date());
            yxStoreOrderMapper.update(storeOrder, wrapper);

            // 增加用户购买次数
            userService.incPayCount(orderInfo.getUid());
            try {
                List<OrderCartItem> yxStoreOrderCartInfos = orderCartInfoService.getBaseMapper()
                        .selectList(Wrappers.<OrderCartItem>lambdaQuery().eq(OrderCartItem::getOid, orderId));
                // 对每一个SPU, SKU增加用户的购买数量
                yxStoreOrderCartInfos.forEach(cartInfo -> {
                    YxStoreCartQueryVo cartInfoDetail = cartInfo.cartInfoVO();
                    redisUtils.increaseDailyPurchaseNum(orderInfo.getUid(), cartInfo.getProductId(),
                            cartInfoDetail.getCartNum());
                    // 对每一个SKU增加用户的购买数量
                    redisUtils.increaseDailySkuPurchaseNum(orderInfo.getUid(), cartInfo.getProductId(),
                            cartInfoDetail.getProductAttrUnique(),
                            cartInfoDetail.getCartNum());
                });

            } catch (Exception e) {
                e.printStackTrace();
                log.warn("redisUtils.increaseDailyPurchaseNum error: {}", e.getMessage());
            }

            // 增加状态
            orderStatusService.create(orderInfo.getId(), OrderLogEnum.PAY_ORDER_SUCCESS.getValue(),
                    OrderLogEnum.PAY_ORDER_SUCCESS.getDesc());

            YxUser userInfo = userService.getById(orderInfo.getUid());
            // 增加流水
            String payTypeMsg = PayTypeEnum.WEIXIN.getDesc();
            // if (PayTypeEnum.YUE.equalsTo(payType)) {
            // payTypeMsg = PayTypeEnum.YUE.getDesc();
            // }
            billService.expend(userInfo.getUid(), "购买商品",
                    BillDetailEnum.CATEGORY_CASH.getValue(),
                    BillDetailEnum.TYPE_3.getValue(),
                    orderInfo.getPayPrice().doubleValue(), userInfo.getNowMoney().doubleValue(),
                    payTypeMsg + orderInfo.getPayPrice() + "元购买商品", orderId);

            // 模板消息支付成功发布事件
            TemplateBean templateBean = TemplateBean.builder()
                    .orderId(orderInfo.getOrderId())
                    .price(orderInfo.getPayPrice().toString())
                    .uid(orderInfo.getUid())
                    .templateType(TemplateListenEnum.TYPE_1.getValue())
                    .build();
            publisher.publishEvent(new TemplateEvent(this, templateBean));

        } finally {
            if (orderLockKey != null) {
                unlockOrder(orderId, clientId);
            }
        }
    }

    /**
     * 支付宝支付
     *
     * @param orderId,支付宝支付 本系统已经集成，请自行根据下面找到代码整合下即可
     * @return
     */
    // @Override
    // public String aliPay(String orderId) throws Exception {
    // AlipayConfig alipay = alipayService.find();
    // if (ObjectUtil.isNull(alipay)) {
    // throw new YshopException("请先配置支付宝");
    // }
    // YxStoreOrderQueryVo orderInfo = getOrderInfo(orderId, null);
    // if (ObjectUtil.isNull(orderInfo)) {
    // throw new YshopException("订单不存在");
    // }
    // if (orderInfo.alreadyPaid()) {
    // throw new YshopException("该订单已支付");
    // }

    // if (orderInfo.getPayPrice().compareTo(BigDecimal.ZERO) <= 0) {
    // throw new YshopException("该支付无需支付");
    // }
    // TradeVo trade = new TradeVo();
    // trade.setOutTradeNo(orderId);
    // String payUrl = alipayService.toPayAsWeb(alipay, trade);
    // return payUrl;
    // }
    @Override
    public Order getByOrderId(String orderId) {
        Order order = baseMapper.selectOne(
                Wrappers.<Order>lambdaQuery().eq(Order::getOrderId, orderId));
        return order;
    }

    /**
     * 订单信息
     *
     * @param unique 支持:orderId,unique,extendOrderId
     * @param uid    用户id
     * @return YxStoreOrderQueryVo
     */
    @Override
    public YxStoreOrderQueryVo getOrderInfo(String unique, Long uid) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(
                i -> i.eq(Order::getId, unique).or().eq(Order::getOrderId, unique).or().eq(Order::getUnique, unique)
                        .or()
                        .eq(Order::getExtendOrderId, unique));

        if (uid != null) {
            wrapper.eq(Order::getUid, uid);
        }

        Order order = yxStoreOrderMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(order)) {
            return null;
        }

        YxStoreOrderQueryVo ov = generator.convert(order, YxStoreOrderQueryVo.class);
        if (OrderInfoEnum.STATUS_AFTER_SALES.equalsTo(order.getStatus())) {
            LambdaQueryWrapper<AfterSales> asWrapper = new LambdaQueryWrapper<>();
            asWrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                    .orderByDesc(AfterSales::getId).last("limit 1");
            AfterSales afterSales = afterSalesService.getOne(asWrapper);
            if (Objects.nonNull(afterSales)) {
                AfterSalesVo afterSalesVo = generator.convert(afterSales, AfterSalesVo.class);
                ov.setAfterSales(afterSalesVo);
            }
        }

        return ov;
    }

    /**
     * 奖励积分
     *
     * @param order 订单
     */
    private void gainUserIntegral(YxStoreOrderQueryVo order) {
        if (order.getGainIntegral().compareTo(BigDecimal.ZERO) > 0) {
            YxUser user = userService.getById(order.getUid());

            // 用户积分增加
            BigDecimal newIntegral = NumberUtil.add(user.getIntegral(),
                    order.getGainIntegral());

            user.setIntegral(newIntegral);
            user.setUid(order.getUid());
            userService.updateById(user);

            // 增加流水
            // billService.income(user.getUid(), "购买商品赠送积分",
            // BillDetailEnum.CATEGORY_POINT.getValue(),
            // BillDetailEnum.POINT_TYPE_9.getValue(),
            // order.getGainIntegral().doubleValue(),
            // newIntegral.doubleValue(),
            // "购买商品赠送" + order.getGainIntegral() + "积分", order.getId().toString());
        }
    }

    @Autowired
    private RedisLock redisLock;

    /**
     * 售出：减库存增加销量，防止超卖
     * 创建订单则售出，此为核心方法
     *
     * @param cartList 购物车
     */
    public void deStockIncSale(Order order, List<YxStoreCartQueryVo> cartList) {
        // 1. 按照SPU 合并购物车数量
        Map<Long, Integer> spuCartNumMap = new HashMap<>();
        Map<Long, Integer> spuLimitBuyMap = new HashMap<>();
        Map<Long, Integer> spuAlreadyBuyNumMap = new HashMap<>();

        for (YxStoreCartQueryVo storeCartVO : cartList) {
            // a. 获取SPU购买数量
            Long spuId = storeCartVO.getProductId();
            Integer cartNum = storeCartVO.getCartNum();
            Integer cartSpuBuyNum = spuCartNumMap.get(spuId);
            if (cartSpuBuyNum == null) {
                cartSpuBuyNum = 0;
            }
            spuCartNumMap.put(spuId, cartSpuBuyNum + cartNum);

            // b. 获取SPU限购数量, 这里重复操作了
            Integer productLimitBuy = storeCartVO.getProductInfo().getLimitBuy();
            if (productLimitBuy != null && productLimitBuy != 0) {
                spuLimitBuyMap.put(spuId, productLimitBuy);
            } else {
                spuLimitBuyMap.put(spuId, 0);
            }
        }

        // 2. 已购数量获取
        for (Map.Entry<Long, Integer> entry : spuLimitBuyMap.entrySet()) {
            Long spuId = entry.getKey();
            String spuLimitBuyCacheKey = StoreOrderService.getSPUBuyLimitCacheKey(order.getUid(), spuId);
            Integer alreadyBuyNum = RedisUtil.get(spuLimitBuyCacheKey);
            if (alreadyBuyNum == null) {
                alreadyBuyNum = 0;
            }
            spuAlreadyBuyNumMap.put(spuId, alreadyBuyNum);
        }

        // 3. SKU、SPU限购逻辑
        for (YxStoreCartQueryVo storeCartVO : cartList) {
            // 限购逻辑
            // 检测用户是否单日内多次购买
            // 1. 获取今日全部订单
            // 2. 查询今日购买的商品及数量
            // 3. 根据商品限购数量判断是否可以继续下单

            // 3.1 SKU限购逻辑
            SKU sku = storeCartVO.getProductInfo().getAttrInfo();
            Integer skuLimitBuy = sku.getLimitBuy();
            String skuLimitBuyCacheKey = StoreOrderService.getSKUBuyLimitCacheKey(storeCartVO.getUid(),
                    sku.getId(),
                    sku.getUnique());

            if (skuLimitBuy != null && skuLimitBuy != 0) {
                Integer skuAlreadyBuyNum = RedisUtil.get(skuLimitBuyCacheKey);
                if (skuAlreadyBuyNum == null) {
                    skuAlreadyBuyNum = 0;
                }
                if (skuAlreadyBuyNum + storeCartVO.getCartNum() > skuLimitBuy) {
                    String msg = String.format("商品: %s, SKU: %s, 限购: %d件", storeCartVO.getProductInfo().getStoreName(),
                            sku.getSku(),
                            skuLimitBuy);
                    throw new YshopException(msg);
                }
            }
        }
        for (YxStoreCartQueryVo storeCartVO : cartList) {
            SKU sku = storeCartVO.getProductInfo().getAttrInfo();
            Integer skuLimitBuy = sku.getLimitBuy();
            String skuLimitBuyCacheKey = StoreOrderService.getSKUBuyLimitCacheKey(storeCartVO.getUid(),
                    sku.getId(),
                    sku.getUnique());
            if (skuLimitBuy != null && skuLimitBuy != 0) {
                Integer skuAlreadyBuyNum = RedisUtil.get(skuLimitBuyCacheKey);
                if (skuAlreadyBuyNum == null) {
                    skuAlreadyBuyNum = 0;
                }
                RedisUtil.incr(skuLimitBuyCacheKey, storeCartVO.getCartNum());
            }
        }

        // 4. SPU 限购
        for (Map.Entry<Long, Integer> entry : spuLimitBuyMap.entrySet()) {
            Long spuId = entry.getKey();
            Integer productLimitBuy = entry.getValue();
            if (productLimitBuy != null && productLimitBuy != 0) {
                Integer spuAlreadyBuyNum = spuAlreadyBuyNumMap.get(spuId);
                Integer spuCartNum = spuCartNumMap.get(spuId);
                if (spuAlreadyBuyNum + spuCartNum > productLimitBuy) {
                    String msg = String.format("商品: %s, 限购: %d件", spuId,
                            productLimitBuy);
                    throw new YshopException(msg);
                }
            }
        }

        // 5. 更新SPU限购缓存
        for (Map.Entry<Long, Integer> entry : spuLimitBuyMap.entrySet()) {
            Long spuId = entry.getKey();
            Integer spuLimitBuy = entry.getValue();
            if (spuLimitBuy != null && spuLimitBuy != 0) {
                String spuLimitBuyCacheKey = StoreOrderService.getSPUBuyLimitCacheKey(order.getUid(), spuId);
                RedisUtil.incr(spuLimitBuyCacheKey, spuCartNumMap.get(spuId));
            }
        }

        // 观察操作的平均完成时间和最长完成时间。锁的过期时间应略高于最长完成时间，以避免在操作未完成时锁自动释放。
        // 等待锁的时间
        // String clientId = snowFlakeService.getMySnowFlake().nextIdStr();
        // long startTime = System.currentTimeMillis();
        long startProcTime = System.currentTimeMillis();

        try {
            startProcTime = System.currentTimeMillis();

            try {
                for (YxStoreCartQueryVo storeCartVO : cartList) {
                    productService.decSkuStock(storeCartVO.getCartNum(), storeCartVO.getProductId(),
                            storeCartVO.getProductAttrUnique(), order.getOrderId());
                }
                long endTime = System.currentTimeMillis();
                log.info("库存降低，销量增加事务完成: oid: {}, 执行时间: {} ms", order.getOrderId(), endTime - startProcTime);
            } catch (Exception ex) {
                log.error("库存降低，销量增加事务失败: oid: {}, {}", order.getOrderId(), ex.getMessage());
                throw ex;
            }
        } catch (Exception e) {
            log.error("销售出库异常: {} {}", order.getOrderId(), e.getMessage());
            e.printStackTrace();
            throw new YshopException("系统繁忙，请稍后再试");
        } finally {
            // !!!确保逆顺序释放锁
        }
    }

    /**
     * 积分抵扣
     *
     * @param userInfo       用户信息
     * @param usedIntegral   使用得积分
     * @param deductionPrice 抵扣的金额
     */
    // private void decIntegral(YxUser userInfo, double usedIntegral, double
    // deductionPrice) {
    // userService.decIntegral(userInfo.getUid(), usedIntegral);
    // billService.expend(userInfo.getUid(), "积分抵扣",
    // BillDetailEnum.CATEGORY_POINT.getValue(),
    // BillDetailEnum.POINT_TYPE_8.getValue(), usedIntegral,
    // userInfo.getIntegral().doubleValue(),
    // "购买商品使用" + usedIntegral + "积分抵扣" + deductionPrice + "元", null);
    // }

    /**
     * 计算奖励的积分
     *
     * @param cartInfo cartInfo
     * @return double
     */
    private BigDecimal getGainIntegral(List<YxStoreCartQueryVo> cartInfo) {
        BigDecimal gainIntegral = BigDecimal.ZERO;

        for (YxStoreCartQueryVo cart : cartInfo) {
            BigDecimal cartInfoGainIntegral = BigDecimal.ZERO;
            // 积分为商品价格
            Double gain = cart.getProductInfo().getPrice().doubleValue();
            if (gain > 0) {
                cartInfoGainIntegral = NumberUtil.round(NumberUtil.mul(cart.getCartNum(),
                        gain), 2);
            }
            gainIntegral = NumberUtil.add(gainIntegral, cartInfoGainIntegral);
        }

        return gainIntegral;
    }

    /**
     * 实际执行退回库存，处理售后商品本身就有瑕疵，报损
     * 1. 未付款订单，取消
     * 2. 未付款订单，关闭
     * 3. 微信退款成功后的回调:尝试一次退库存
     * 退回库存可以稍慢
     * 假设操作非常快，可以在几毫秒到一两秒内完成，你可能设置如下:
     * <p>
     * 锁定时间： 5-10秒，这给出了足够的余地来处理正常的操作，并且包含了一些额外的时间以防有延迟。
     * 等待时间： 1-2秒，如果你的应用可以容忍短暂的延迟，也能防止客户端无限等待。
     * 等待间隔： 50-100毫秒，定义在两次尝试获取锁之间的休眠时间。
     *
     * @param order 订单
     */
    @Transactional
    public void regressionStockAndReportLoss(YxStoreOrderQueryVo order, List<OrderCartItem> cartInfoList,
            AfterSales afterSales) {
        // !!! 库存要根据微信退款状态
        if (OrderRefundStatusEnum.REFUND_STATUS_2_DONE.equalsTo(order.getWechatRefundStatus())) {
            // 跳过已经退款的订单
            log.info("订单: {} rs: {} wrs: {} 已经退款，不进行库存回退", order.getId(), order.getRefundStatus(),
                    order.getWechatRefundStatus());
            return;
        }

        List<AfterSalesItem> storeAfterSalesItems = storeAfterSalesItemMapper
                .selectList(
                        Wrappers.<AfterSalesItem>lambdaQuery().eq(AfterSalesItem::getStoreAfterSalesId,
                                afterSales.getId()));

        String cartId = order.getCartId();
        log.info("回归库存, 订单对应的购物车ID: {}", cartId);

        // 观察操作的平均完成时间和最长完成时间。锁的过期时间应略高于最长完成时间，以避免在操作未完成时锁自动释放。
        // 等待锁的时间
        List<OrderCartItem> needRefundItems = new ArrayList<>();

        for (OrderCartItem cartInfo : cartInfoList) {
            YxStoreCartQueryVo cart = cartInfo.cartInfoVO();
            AfterSalesItem afterSalesItem = storeAfterSalesItems.stream()
                    .filter(x -> com.leway.utils.StringUtils.equals(x.getProductAttrUnique(),
                            cart.getProductAttrUnique()))
                    .findFirst().orElse(null);
            if (afterSalesItem != null) {
                needRefundItems.add(cartInfo);
            }
        }
        long startProcTime = System.currentTimeMillis();

        // 执行库存增减操作
        try {
            for (OrderCartItem cartInfo : needRefundItems) {
                YxStoreCartQueryVo cart = cartInfo.cartInfoVO();

                AfterSalesItem asItem = storeAfterSalesItems.stream()
                        .filter(x -> com.leway.utils.StringUtils.equals(x.getProductAttrUnique(),
                                cart.getProductAttrUnique()))
                        .findFirst().orElse(null);
                // 部分回滚, num < cart num
                // 退回来1个有问题的商品，并且报损
                int num = asItem.getNum();
                log.info("回滚销量，不回滚库存: spu: {}, sku: {}, num: {}", cart.getProductId(),
                        cart.getProductAttrUnique(),
                        num);

                // 坏掉的库存，保持不变，减少销量
                productService.keepSkuStockDecSales(num, cart.getProductId(),
                        cart.getProductAttrUnique(),
                        order.getOrderId());

                // 报损记录，此处默认寄回的商品都是坏的

                // 2.2 新增库存报损记录
                SKU sku = cart.getProductInfo().getAttrInfo();
                Long skuId = sku.getId();
                InventoryRecord record = inventoryRecordService.findBySkuId(skuId);

                InventoryTransaction it = new InventoryTransaction();
                it.setOperatorId(0l);
                it.setInventoryId(record.getInventoryId());
                it.setCost(null);
                it.setBatchNumber("-");
                it.setTransactionType(InventoryTransaction.TRANSACTION_TYPE_WRITE_OFF);
                it.setQuantity(-num);
                it.setTransactionDate(new Date());
                it.setRemarks(String.format("订单 %s 退货，商品报损", order.getOrderId()));

                // save point 1
                it = inventoryTransactionService.saveTransaction(it);

                // 2.3. 修改库存
                int newQuantity = record.getCurrentQuantity() - num;
                record.setCurrentQuantity(newQuantity);
                // save point 2
                record = inventoryRecordService.saveInventoryRecord(record);

                Integer skuLimitBuy = sku.getLimitBuy();
                if (skuLimitBuy != null && skuLimitBuy != 0) {
                    String skuLimitBuyCacheKey = StoreOrderService.getSKUBuyLimitCacheKey(cart.getUid(),
                            cart.getProductId(),
                            cart.getProductAttrUnique());
                    RedisUtil.decr(skuLimitBuyCacheKey, num);
                }

                Integer spuLimitBuy = cart.getProductInfo().getLimitBuy();
                if (spuLimitBuy != null && spuLimitBuy != 0) {
                    String spuLimitBuyCacheKey = StoreOrderService.getSPUBuyLimitCacheKey(cart.getUid(),
                            cart.getProductId());
                    RedisUtil.decr(spuLimitBuyCacheKey, num);
                }

            }
            long endTime = System.currentTimeMillis();
            log.info("回滚库存完成，order: {}, 执行时间: {} ms", order.getOrderId(), endTime - startProcTime);
        } catch (Exception ex) {
            log.error("回滚库存事务失败: oid: {}, {}", order.getOrderId(), ex.getMessage());
            throw ex;
        }

    }

    /**
     * 实际执行退回库存
     * 1. 未付款订单，取消
     * 2. 未付款订单，关闭
     * 3. 微信退款成功后的回调:尝试一次退库存
     * 退回库存可以稍慢
     * 假设操作非常快，可以在几毫秒到一两秒内完成，你可能设置如下:
     * <p>
     * 锁定时间： 5-10秒，这给出了足够的余地来处理正常的操作，并且包含了一些额外的时间以防有延迟。
     * 等待时间： 1-2秒，如果你的应用可以容忍短暂的延迟，也能防止客户端无限等待。
     * 等待间隔： 50-100毫秒，定义在两次尝试获取锁之间的休眠时间。
     *
     * @param order 订单
     */
    public void regressionStock(YxStoreOrderQueryVo order, List<OrderCartItem> cartInfoList, AfterSales afterSales) {
        // !!! 库存要根据微信退款状态
        if (OrderRefundStatusEnum.REFUND_STATUS_2_DONE.equalsTo(order.getWechatRefundStatus())) {
            // 跳过已经退款的订单
            log.info("订单: {} rs: {} wrs: {} 已经退款，不进行库存回退", order.getId(), order.getRefundStatus(),
                    order.getWechatRefundStatus());
            return;
        }
        log.info("订单: {} 回归库存开始", order.getId());

        // String cartId = order.getCartId();
        // log.info("回归库存, 订单对应的购物车ID: {}", cartId);

        List<AfterSalesItem> storeAfterSalesItems = new ArrayList<>();
        if (afterSales != null) {
            storeAfterSalesItems = storeAfterSalesItemMapper
                    .selectList(
                            Wrappers.<AfterSalesItem>lambdaQuery().eq(AfterSalesItem::getStoreAfterSalesId,
                                    afterSales.getId()));
        }

        // 观察操作的平均完成时间和最长完成时间。锁的过期时间应略高于最长完成时间，以避免在操作未完成时锁自动释放。
        // String clientId = snowFlakeService.getMySnowFlake().nextIdStr();
        // long startTime = System.currentTimeMillis();
        long startProcTime = System.currentTimeMillis();

        try {
            // 执行库存增减操作
            try {
                for (OrderCartItem cartInfo : cartInfoList) {
                    YxStoreCartQueryVo cart = cartInfo.cartInfoVO();
                    int refundNum = cart.getCartNum();
                    if (afterSales != null) {
                        AfterSalesItem afterSalesItem = storeAfterSalesItems.stream()
                                .filter(x -> com.leway.utils.StringUtils.equals(x.getProductAttrUnique(),
                                        cart.getProductAttrUnique()))
                                .findFirst().orElse(null);

                        if (afterSalesItem != null) {
                            refundNum = afterSalesItem.getNum();
                        } else {
                            // 该商品未退款
                            refundNum = 0;
                        }
                    }
                    log.info("回滚库存: order: {}, spu: {}, sku: {}, num: {}", order.getOrderId(), cart.getProductId(),
                            cart.getProductAttrUnique(),
                            refundNum);

                    productService.incSkuStock(refundNum, cart.getProductId(), cart.getProductAttrUnique(),
                            order.getOrderId());
                    // 限购回滚，由于订单取消等造成库存回滚

                    // log.info("限购回滚开始: {}", order.getOrderId());
                    Integer skuLimitBuy = cart.getProductInfo().getAttrInfo().getLimitBuy();
                    if (skuLimitBuy != null && skuLimitBuy != 0) {
                        // 对限商品购数量进行检测
                        String skuLimitBuyCacheKey = StoreOrderService.getSKUBuyLimitCacheKey(cart.getUid(),
                                cart.getProductId(),
                                cart.getProductAttrUnique());
                        RedisUtil.decr(skuLimitBuyCacheKey, refundNum);
                    }

                    Integer spuLimitBuy = cart.getProductInfo().getLimitBuy();
                    if (spuLimitBuy != null && spuLimitBuy != 0) {
                        String spuLimitBuyCacheKey = StoreOrderService.getSPUBuyLimitCacheKey(cart.getUid(),
                                cart.getProductId());
                        RedisUtil.decr(spuLimitBuyCacheKey, refundNum);
                    }
                    // log.info("限购回滚结束: {}", order.getOrderId());

                    // log.info("订单: {} 回归库存结束", order.getOrderId());

                }
                long endTime = System.currentTimeMillis();
                log.info("回滚库存完成，order: {}, 执行时间: {} ms", order.getOrderId(), endTime - startProcTime);
            } catch (Exception ex) {
                log.error("回滚库存事务失败: oid: {}, {}", order.getOrderId(), ex.getMessage());
                // ex.printStackTrace();
                throw ex;
            }
        } catch (Exception e) {
            log.error("回滚库存异常: {} {}", order.getOrderId(), e.getMessage());
            e.printStackTrace();
            throw new YshopException("系统繁忙，请稍后再试");
        } finally {

        }

    }

    /**
     * 退回积分
     *
     * @param order 订单
     */
    private void regressionIntegral(YxStoreOrderQueryVo order, BigDecimal integral) {

        if (order.notPaid() || OrderRefundStatusEnum.REFUND_STATUS_2_DONE.equalsTo(order.getRefundStatus())) {
            // 未支付、已退款订单不会发生回退积分
            log.info("订单: {} 未支付、已退款订单不会发生回退积分", order.getId(), order.getRefundStatus());
            return;
        }

        // 确认收货的订单，才可能有积分需要回退
        if (!OrderInfoEnum.STATUS_AFTER_SALES.equalsTo(order.getStatus())) {
            // 不在售后状态，不回退积分
            log.info("订单: {} 不在售后状态，不回退积分", order.getId(), order.getStatus());
            return;
        }

        // 没到已完成状态，不能回退积分
        if (!OrderInfoEnum.STATUS_2_DONE.equalsTo(order.getRefundSuspendStatus())) {
            log.info("订单: {} 售后前没确认收货，不回退积分", order.getId(), order.getRefundSuspendStatus());
            return;
        }

        YxUser yxUser = userService.getById(order.getUid());
        log.info("订单: {} 当前积分: {}, 用户积分减少: {}", order.getId(), yxUser.getIntegral(), integral);

        // 用户积分减少
        BigDecimal newIntegral = NumberUtil.sub(yxUser.getIntegral(), integral);

        yxUser.setIntegral(newIntegral);
        userService.updateById(yxUser);

        // 增加流水
        // billService.income(yxUser.getUid(), "积分回退",
        // BillDetailEnum.CATEGORY_POINT.getValue(),
        // BillDetailEnum.POINT_TYPE_8.getValue(),
        // order.getUseIntegral().doubleValue(),
        // newIntegral.doubleValue(),
        // "购买商品失败,回退积分" + order.getUseIntegral(), order.getId().toString());

        // 更新回退积分
        // YxStoreOrder storeOrder = new YxStoreOrder();
        // storeOrder.setBackIntegral(order.getUseIntegral());
        // storeOrder.setId(order.getId());
        // yxStoreOrderMapper.updateById(storeOrder);
    }

    /**
     * 获取订单缓存
     *
     * @param uid uid
     * @param key key
     * @return CacheDto
     */
    private OrderInfoCacheDto getCacheOrderInfo(Long uid, String key) {
        Object obj = redisUtils.get(ShopConstants.YSHOP_ORDER_CACHE_KEY + uid + key);
        if (obj == null) {
            return null;
        }
        return JSON.parseObject(obj.toString(), OrderInfoCacheDto.class);
    }

    /**
     * 删除订单缓存
     *
     * @param uid uid
     * @param key key
     */
    private void delCacheOrderInfo(Long uid, String key) {
        redisUtils.del(ShopConstants.YSHOP_ORDER_CACHE_KEY + uid + key);
    }

    /**
     * 缓存订单（购物车信息、价格信息、其他信息）
     *
     * @param uid        uid
     * @param cartInfo   cartInfo 购物车信息（List）
     * @param priceGroup priceGroup
     * @return key string
     */
    private String cacheOrderInfo(Long uid, List<YxStoreCartQueryVo> cartInfo, PriceGroupDto priceGroup) {
        String key = snowFlakeService.getMySnowFlake().nextIdStr();

        OrderInfoCacheDto cacheDTO = new OrderInfoCacheDto();
        cacheDTO.setCartInfo(cartInfo);
        cacheDTO.setPriceGroup(priceGroup);

        redisUtils.set(ShopConstants.YSHOP_ORDER_CACHE_KEY + uid + key,
                JSON.toJSONString(cacheDTO),
                ShopConstants.YSHOP_ORDER_CACHE_TIME);
        return key;
    }

    /**
     * 获取订单价格组(基于购物车)
     * 包含
     * 1. 商品总价
     * 2. 运费
     * 3. 保价费用
     *
     * @param cartInfo 购物车列表
     * @return PriceGroupDto
     */
    private PriceGroupDto getOrderPriceGroup(List<YxStoreCartQueryVo> cartInfo,
            YxUserAddress userAddress) {

        // 运费
        BigDecimal storePostage = this.handlePostage(cartInfo, userAddress);

        // 满额包邮 金额
        // 120.00 2025-04-25 满额包邮
        final BigDecimal storeFreePostage = SystemConfigConstants.STORE_FREE_POSTAGE_AMOUNT;

        // 保价金额
        BigDecimal totalInsurePrice = this.getOrderInsureSumPrice(cartInfo);
        // 商品总价
        BigDecimal totalPrice = this.getOrderSumPrice(cartInfo); // 获取订单总金额

        log.info("计算运费: {}", storePostage);


        PriceGroupDto priceGroupDTO = new PriceGroupDto();
        // 邮费
        priceGroupDTO.setStorePostage(storePostage);
        // 默认包邮金额
        priceGroupDTO.setStoreFreePostage(storeFreePostage);

        priceGroupDTO.setTotalPrice(totalPrice);
        priceGroupDTO.setTotalInsurePrice(totalInsurePrice);
        // 会员价格
        // priceGroupDTO.setVipPrice(vipPrice);
        return priceGroupDTO;
    }

    /**
     * 预估邮费，当做首件、首重
     *
     * @param product
     * @param userAddress
     * @return
     */
    @Override
    public BigDecimal queryPostage(ProductQueryVo product, YxUserAddress userAddress) {
        BigDecimal storePostage = BigDecimal.ZERO;

        // 检查是否满足全局满额包邮条件
        BigDecimal storeFreePostage = SystemConfigConstants.STORE_FREE_POSTAGE_AMOUNT;
        // if (storeFreePostage.compareTo(BigDecimal.ZERO) != 0) {
        // BigDecimal productPrice = product.getPrice();
        // if (productPrice.compareTo(storeFreePostage) >= 0) {
        // // TODO: 超过限额就是包邮，但不包括特殊地区
        // return storePostage;
        // }
        // }

        if (userAddress == null) {
            return storePostage;
        }
        if (userAddress.getCityId() == null) {
            return storePostage;
        }

        // 城市包括默认
        int cityId = userAddress.getCityId();
        List<Integer> citys = new ArrayList<>();
        citys.add(cityId);
        citys.add(0);

        boolean debugMode = false;
        if (debugMode) {
            YxSystemCity city = systemCityService.getByCityId(cityId);
            log.info("address {} 城市ID:{} {}", userAddress.getId(), cityId, userAddress.getCity());
            if (city != null) {
                log.info("城市:{} ", city.getName());
            }
        }

        // 获取运费模板
        Integer tempId = product.getTempId();

        // 获取商品用到的运费模板
        YxShippingTemplates shippingTemplate = shippingTemplatesService
                .getOne(Wrappers.<YxShippingTemplates>lambdaQuery()
                        .eq(YxShippingTemplates::getId, tempId));

        if (debugMode) {
            log.info("运费模板：{}", shippingTemplate.getName());
        }

        // 获取运费模板区域列表按照城市排序
        List<YxShippingTemplatesRegion> shippingTemplatesRegionList = shippingTemplatesRegionService
                .list(Wrappers.<YxShippingTemplatesRegion>lambdaQuery()
                        .eq(YxShippingTemplatesRegion::getTempId, tempId)
                        .in(YxShippingTemplatesRegion::getCityId, citys)
                        .orderByAsc(YxShippingTemplatesRegion::getCityId));

        // 提取运费模板类型
        // 按件数、按重量、按体积
        int shippingTempType = shippingTemplate.getType();

        // 提取运费模板有相同值覆盖
        Map<Integer, YxShippingTemplatesRegion> shippingTemplatesRegionMap = shippingTemplatesRegionList.stream()
                .collect(Collectors.toMap(YxShippingTemplatesRegion::getTempId,
                        YxShippingTemplatesRegion -> YxShippingTemplatesRegion,
                        (key1, key2) -> key2));

        // 没有设置运费模板
        if (tempId == null) {
            return storePostage;
        }

        // 根据模板类型获取相应的数量
        double num = 0d;
        if (ShippingTempEnum.TYPE_1.equalsTo(shippingTempType)) {
            // 假设1件
            num = 1d;
        } else if (ShippingTempEnum.TYPE_2.equalsTo(shippingTempType)) {
            // 1kg
            num = 1d;
        } else if (ShippingTempEnum.TYPE_3.equalsTo(shippingTempType)) {
            // 1m立方
            num = 1d;
        }

        /**
         * 假设只买一件
         */
        BigDecimal buyNum = BigDecimal.valueOf(1);

        /**
         * 获取区域邮费模板
         */
        YxShippingTemplatesRegion shippingTemplatesRegion = shippingTemplatesRegionMap.get(tempId);

        /**
         * 预估价格、因为存在价格层面包邮
         */
        BigDecimal price = NumberUtil.round(NumberUtil.mul(buyNum,
                product.getPrice()), 2);

        TemplateDto templateDTO = TemplateDto.builder()
                // 计算数值，例如件数、体积、重量
                .number(num)
                .price(price)
                .first(shippingTemplatesRegion.getFirst().doubleValue())
                .firstPrice(shippingTemplatesRegion.getFirstPrice())
                ._continue(shippingTemplatesRegion.getContinues().doubleValue())
                .continuePrice(shippingTemplatesRegion.getContinuePrice())
                .tempId(tempId)
                .cityId(cityId)
                .build();

        // 处理包邮情况
        // 注释掉单独的包邮检查，因为这个方法主要用于查询单个商品的邮费
        // 包邮逻辑应该在订单级别的handlePostage方法中统一处理
        /*
        boolean free = false;
        Long count = shippingTemplatesFreeService.count(Wrappers.<YxShippingTemplatesFree>lambdaQuery()
                .eq(YxShippingTemplatesFree::getTempId, templateDTO.getTempId())
                .eq(YxShippingTemplatesFree::getCityId, templateDTO.getCityId())
                .le(YxShippingTemplatesFree::getNumber, templateDTO.getNumber())
                .le(YxShippingTemplatesFree::getPrice, templateDTO.getPrice()));

        // 满足包邮条件剔除
        if (count > 0) {
            free = true;
        }

        if (free) {
            if (debugMode) {
                log.info("包邮，计算费用: {}", storePostage);
            }
            return storePostage;
        }
        */

        // 不包邮情况
        // 处理区域邮费
        if (Double.compare(templateDTO.getNumber(), templateDTO.getFirst()) <= 0) {
            // 首次
            storePostage = NumberUtil.round(NumberUtil.add(storePostage,
                    templateDTO.getFirstPrice()), 2);
        } else {
            // 续费
            BigDecimal firstPrice = NumberUtil.add(storePostage, templateDTO.getFirstPrice());

            if (templateDTO.get_continue() <= 0) {
                storePostage = firstPrice;
            } else {
                // 续件平均值且向上取整数
                double average = Math.ceil(NumberUtil.div(NumberUtil.sub(templateDTO.getNumber(),
                        templateDTO.getFirst()),
                        templateDTO.get_continue().doubleValue()));
                // 最终邮费
                storePostage = NumberUtil.add(firstPrice, NumberUtil.mul(average,
                        templateDTO.getContinuePrice()));
            }
        }

        return storePostage;
    }

    /**
     * 根据运费模板算法返回邮费
     *
     * @param cartInfo    购物车
     * @param userAddress 地址
     * @return double
     */
    @Override
    public BigDecimal handlePostage(List<YxStoreCartQueryVo> cartInfo, YxUserAddress userAddress) {
        // 商品总价
        BigDecimal totalPrice = this.getOrderSumPrice(cartInfo); // 获取订单总金额

        // 满额包邮 金额
        // 120.00 2025-04-25 满额包邮
        final BigDecimal storeFreePostage = SystemConfigConstants.STORE_FREE_POSTAGE_AMOUNT;

        // 基础运费 10 元
        BigDecimal baseStorePostage = new BigDecimal(10);
        // 注释掉早期的包邮逻辑，在最后统一处理
        // if (storeFreePostage.compareTo(BigDecimal.ZERO) != 0 &&
        //         totalPrice.compareTo(storeFreePostage) >= 0) {
        //     // 订单金额超过120元，包邮
        //     baseStorePostage = BigDecimal.ZERO;
        // }

        if (userAddress == null) {
            // 用户未设置地址，计算为基础运费
            return baseStorePostage;
        }

        if (userAddress.getCityId() == null) {
            // 用户地址异常，计算为基础运费
            return baseStorePostage;
        }

        //
        // part 1 数据准备
        //

        // 检查全局包邮规则：如果订单总价≥120元，则应该包邮
        // 但需要考虑偏远地区的特殊情况
        boolean shouldApplyGlobalFreeShipping = false;
        if (storeFreePostage.compareTo(BigDecimal.ZERO) != 0 &&
                totalPrice.compareTo(storeFreePostage) >= 0) {
            shouldApplyGlobalFreeShipping = true;
        }

        // 判断是否为偏远地区：通过地址信息判断
        boolean isRemoteArea = isRemoteArea(userAddress);

        // 城市包括默认
        int userCityId = userAddress.getCityId();
        List<Integer> cityIDs = new ArrayList<>();
        cityIDs.add(userCityId);
        // 默认城市
        cityIDs.add(0);


        /** 获取购物车商品列表 */
        List<ProductQueryVo> storeProductVOList = cartInfo
                .stream()
                .map(YxStoreCartQueryVo::getProductInfo)
                .collect(Collectors.toList());

        /**
         * 运费模板IDs列表
         */
        List<Integer> postageTmplIdS = storeProductVOList
                .stream()
                .map(ProductQueryVo::getTempId)
                .collect(Collectors.toList());

        /** 获取商品用到的运费模板 */
        List<YxShippingTemplates> shippingTemplatesList = shippingTemplatesService
                .list(Wrappers.<YxShippingTemplates>lambdaQuery()
                        .in(YxShippingTemplates::getId, postageTmplIdS));

        // 获取运费模板区域列表按照城市排序, 确保ID:0城市在首位
        List<YxShippingTemplatesRegion> shippingTemplatesRegionList = shippingTemplatesRegionService
                .list(Wrappers.<YxShippingTemplatesRegion>lambdaQuery()
                        .in(YxShippingTemplatesRegion::getTempId, postageTmplIdS)
                        .in(YxShippingTemplatesRegion::getCityId, cityIDs)
                        .orderByAsc(YxShippingTemplatesRegion::getCityId));

        // 提取运费模板类型（按件数、按重量、按体积）
        Map<Integer, Integer> shippingTemplatesTypeMap = shippingTemplatesList
                .stream()
                .collect(Collectors.toMap(YxShippingTemplates::getId,
                        YxShippingTemplates::getType));

        // 映射(运费模板ID->地域运费模板)
        Map<Integer, YxShippingTemplatesRegion> shippingTemplatesRegionMap = shippingTemplatesRegionList.stream()
                .collect(Collectors.toMap(YxShippingTemplatesRegion::getTempId,
                        YxShippingTemplatesRegion -> YxShippingTemplatesRegion,
                        (key1, key2) -> key2));

        // 映射(商品运费模板ID->模板数据)，即运费数据
        Map<Integer, TemplateDto> templateDTOMap = new HashMap<>();

        for (YxStoreCartQueryVo storeCartVO : cartInfo) {
            ProductQueryVo product = storeCartVO.getProductInfo();
            Integer tempId = product.getTempId();

            // 商品没有设置运费模板
            if (tempId == null) {
                log.warn("商品没有设置运费模板，跳过计算运费. 商品ID:{} 商品名称:{}", product.getId(), product.getStoreName());
                return baseStorePostage;
            }

            // 根据模板类型获取相应的数量(件数、重量、体积)
            double num = 0d;

            Integer shippingTempType = shippingTemplatesTypeMap.get(tempId);
            if (shippingTempType == null) {
                log.warn("运费模板类型不存在, 模板ID: {}, map: {}, 商品ID:{} 商品名称:{}", tempId, shippingTemplatesTypeMap.size(), product.getId(), product.getStoreName());
                return baseStorePostage;
            }

            if (ShippingTempEnum.TYPE_1.equalsTo(shippingTempType)) {
                // 1. 按件数
                num = storeCartVO.getCartNum().doubleValue();
            } else if (ShippingTempEnum.TYPE_2.equalsTo(shippingTempType)) {
                // !!! 2. 按重量，这里需要换算，因为90希望填写克重
                BigDecimal weightInG = product.getAttrInfo().getWeight();
                BigDecimal weightInKG = NumberUtil.div(weightInG, BigDecimal.valueOf(1000));
                num = NumberUtil.mul(storeCartVO.getCartNum(), weightInKG).doubleValue();
                // log.info("共计重量：{}KG", num);
            } else if (ShippingTempEnum.TYPE_3.equalsTo(shippingTempType)) {
                // 3. 按体积
                num = NumberUtil.mul(storeCartVO.getCartNum(),
                        storeCartVO.getProductInfo().getAttrInfo().getVolume()).doubleValue();
            }

            YxShippingTemplatesRegion shippingTemplatesRegion = shippingTemplatesRegionMap.get(tempId);
            BigDecimal price = NumberUtil.round(NumberUtil.mul(storeCartVO.getCartNum(),
                    storeCartVO.getTruePrice()), 2);

            // 根据运费合计
            if (!templateDTOMap.containsKey(tempId)) {
                // 没包含，则新增计入
                TemplateDto templateDTO = TemplateDto.builder()
                        .number(num)
                        .price(price)
                        // 首(重)
                        .first(shippingTemplatesRegion.getFirst().doubleValue())
                        // 首（重）价格
                        .firstPrice(shippingTemplatesRegion.getFirstPrice())
                        // 续(重)
                        ._continue(shippingTemplatesRegion.getContinues().doubleValue())
                        // 续(重)价格
                        ._continue(shippingTemplatesRegion.getContinues().doubleValue())
                        .continuePrice(shippingTemplatesRegion.getContinuePrice())
                        .tempId(tempId)
                        .cityId(userCityId)
                        .build();
                templateDTOMap.put(tempId, templateDTO);
            } else {
                // 已包含则累加
                TemplateDto templateDTO = templateDTOMap.get(tempId);
                templateDTO.setNumber(templateDTO.getNumber() + num);
                templateDTO.setPrice(NumberUtil.add(templateDTO.getPrice().doubleValue(), price));
            }
        }

        //
        // part 2 计算运费
        //
        // 因为有默认区域（0）全国，所以需要初始化运费为0
        BigDecimal calcStorePostage = BigDecimal.ZERO;

        // 2.1 处理单独指定的包邮情况
        // 修改包邮检查逻辑，考虑偏远地区的特殊情况
        // 只有在非偏远地区且满足全局包邮条件时才应用包邮
        jj: for (Map.Entry<Integer, TemplateDto> entry : templateDTOMap.entrySet()) {
            // 模板ID
            Integer tmplId = entry.getKey();
            // 模板数据
            TemplateDto mapValue = entry.getValue();

            Long count = shippingTemplatesFreeService.count(Wrappers.<YxShippingTemplatesFree>lambdaQuery()
                    .eq(YxShippingTemplatesFree::getTempId, mapValue.getTempId())
                    .eq(YxShippingTemplatesFree::getCityId, mapValue.getCityId())
                    // (数值满足包邮)
                    .le(YxShippingTemplatesFree::getNumber, mapValue.getNumber())
                    // (价格满足包邮)
                    .le(YxShippingTemplatesFree::getPrice, mapValue.getPrice()));

            // 只有在满足包邮条件且不是偏远地区时才剔除运费
            if (count > 0) {
                if (!isRemoteArea && shouldApplyGlobalFreeShipping) {
                    // 非偏远地区且满足全局包邮条件，才剔除运费
                    templateDTOMap.remove(tmplId);
                    break jj;
                }
            }
        }

        // 2.2 处理区域邮费， 默认全国的cityId是0
        boolean isFirst = true; // 购物车中商品累加时候，isFirst表示是否是第一个商品运费模板，cart已经按照tempId合并
        for (TemplateDto templateDTO : templateDTOMap.values()) {
            if (isFirst) {// 首件
                // 只满足首件
                if (Double.compare(templateDTO.getNumber(), templateDTO.getFirst()) <= 0) {
                    calcStorePostage = NumberUtil.round(NumberUtil.add(calcStorePostage,
                            templateDTO.getFirstPrice()), 2);
                } else {
                    // 收件运费
                    BigDecimal firstPrice = NumberUtil.add(calcStorePostage, templateDTO.getFirstPrice());

                    if (templateDTO.get_continue() <= 0) {
                        // 无续件
                        calcStorePostage = firstPrice;
                    } else {
                        // 续件平均值且向上取整数
                        double average = Math.ceil(NumberUtil.div(NumberUtil.sub(templateDTO.getNumber(),
                                templateDTO.getFirst()),
                                templateDTO.get_continue().doubleValue()));
                        // 最终邮费
                        calcStorePostage = NumberUtil.add(firstPrice, NumberUtil.mul(average,
                                templateDTO.getContinuePrice()));
                    }
                }
                isFirst = false;
            } else {
                // 多件直接在以前的基数继续续建
                if (templateDTO.get_continue() > 0) {
                    // 续件平均值且向上取整数
                    double average = Math.ceil(
                            NumberUtil.div(
                                    templateDTO.getNumber(),
                                    templateDTO.get_continue()));
                    // 最终邮费
                    calcStorePostage = NumberUtil.add(calcStorePostage.doubleValue(), NumberUtil.mul(average,
                            templateDTO.getContinuePrice()));
                }
            }
        }

        log.info("邮费计算最终阶段 - 订单总价: {}, 满额包邮金额: {}, 是否应该包邮: {}, 计算出的邮费: {}",
            totalPrice, storeFreePostage, shouldApplyGlobalFreeShipping, calcStorePostage);

        // 应用全局包邮规则：如果订单总价≥120元，则包邮
        // 但偏远地区不享受包邮，需要检查用户地址是否为偏远地区
        if (shouldApplyGlobalFreeShipping) {
            log.info("邮费计算最终阶段 - 订单总价: {}, 满额包邮金额: {}, 是否应该包邮: {}, 计算出的邮费: {}",
                totalPrice, storeFreePostage, shouldApplyGlobalFreeShipping, calcStorePostage);
            log.info("进入包邮规则检查逻辑");

            // 检查是否为偏远地区：直接通过计算出的邮费来判断
            // 如果计算出的邮费大于基础邮费（10元），则认为是偏远地区
            BigDecimal originalBasePostage = new BigDecimal(10);
            boolean isRemoteArea = calcStorePostage.compareTo(originalBasePostage) > 0;

            log.info("包邮规则检查 - 订单总价: {}, 原始基础邮费: {}, 单商品邮费: {}, 是否偏远地区: {}",
                totalPrice, originalBasePostage, calcStorePostage, isRemoteArea);

            if (!isRemoteArea) {
                // 非偏远地区，应用包邮规则
                log.info("非偏远地区，应用包邮规则，返回0元邮费");
                return BigDecimal.ZERO;
            }
            // 偏远地区保持原有运费，不享受包邮
            log.info("偏远地区，不享受包邮，保持原有运费: {}", calcStorePostage);
        }

        return calcStorePostage;
    }

    /**
     * 判断是否为偏远地区
     *
     * @param userAddress 用户地址
     * @return true表示偏远地区，false表示普通地区
     */
    private boolean isRemoteArea(YxUserAddress userAddress) {
        if (userAddress == null) {
            return false;
        }

        String province = userAddress.getProvince();
        if (province == null) {
            return false;
        }

        // 根据省份判断偏远地区
        // 新疆、西藏、青海、甘肃、宁夏、内蒙古等为偏远地区
        return province.contains("新疆") || province.contains("西藏") ||
               province.contains("青海") || province.contains("甘肃") ||
               province.contains("宁夏") || province.contains("内蒙古");
    }

    /**
     * 根据购物车获取订单中的商品总价
     *
     * @param cartInfo 购物车
     * @return Double
     */
    private BigDecimal getOrderSumPrice(List<YxStoreCartQueryVo> cartInfo) {
        BigDecimal sumPrice = BigDecimal.ZERO;
        for (YxStoreCartQueryVo storeCart : cartInfo) {
            sumPrice = NumberUtil.add(sumPrice, NumberUtil.mul(storeCart.getCartNum(), storeCart.getTruePrice()));
        }
        return sumPrice;
    }

    /**
     * 保价总金额
     * 不对sku设置保价
     */
    private BigDecimal getOrderInsureSumPrice(List<YxStoreCartQueryVo> cartInfo) {
        BigDecimal sumPrice = BigDecimal.ZERO;
        for (YxStoreCartQueryVo storeCart : cartInfo) {
            ProductQueryVo product = storeCart.getProductInfo();
            // !!! 对spu进行保价设置
            sumPrice = NumberUtil.add(sumPrice, NumberUtil.mul(storeCart.getCartNum(), product.getInsurePrice()));
        }
        return sumPrice;
    }

    // =======================================================//

    /**
     * 根据商品分类统计订单占比
     *
     * @return OrderCountDto
     */
    @Override
    public OrderCountDto getOrderCount() {
        // 获取所有订单转态为已支付的
        List<CountDto> nameList = storeCartService.findCateName();
        Map<String, Integer> childrenMap = new HashMap<>();
        nameList.forEach(i -> {
            if (i != null) {
                if (childrenMap.containsKey(i.getCatename())) {
                    childrenMap.put(i.getCatename(), childrenMap.get(i.getCatename()) + 1);
                } else {
                    childrenMap.put(i.getCatename(), 1);
                }
            }

        });
        List<OrderCountDto.OrderCountData> list = new ArrayList<>();
        List<String> columns = new ArrayList<>();
        childrenMap.forEach((k, v) -> {
            OrderCountDto.OrderCountData orderCountData = new OrderCountDto.OrderCountData();
            orderCountData.setName(k);
            orderCountData.setValue(v);
            columns.add(k);
            list.add(orderCountData);
        });
        OrderCountDto orderCountDto = new OrderCountDto();
        orderCountDto.setColumn(columns);
        orderCountDto.setOrderCountDatas(list);
        return orderCountDto;
    }

    /**
     * 首页订单/用户等统计
     *
     * @return OrderTimeDataDto
     */
    @Override
    public OrderTimeDataDto getOrderTimeData() {
        OrderTimeDataDto orderTimeDataDto = new OrderTimeDataDto();

        ShoperOrderTimeDataVo shoperOrderTimeData = this.getShoperOrderTimeData();

        BeanUtil.copyProperties(shoperOrderTimeData, orderTimeDataDto);

        // orderTimeDataDto.setUserCount(userService.count());
        QueryWrapper<YxUser> qu = new QueryWrapper<>();
        qu = qu.eq("status", 1);
        orderTimeDataDto.setUserCount(userService.count(qu));

        orderTimeDataDto.setOrderCount(this.count());

        // orderTimeDataDto.setPriceCount(yxStoreOrderMapper.sumTotalPrice());
        // orderTimeDataDto.setGoodsCount(productService.count());

        return orderTimeDataDto;
    }

    /**
     * 返回本月订单金额与数量
     *
     * @return map
     */
    @Override
    public Map<String, Object> chartCount() {
        Map<String, Object> map = new LinkedHashMap<>();
        Date nowMonth = DateUtil.beginOfMonth(new Date());

        map.put("chart", yxStoreOrderMapper.chartList(nowMonth));
        map.put("chartT", yxStoreOrderMapper.chartListT(nowMonth));

        return map;
    }

    /**
     * 根据ID回退库存和积分，退款回调专用
     */
    @Override
    public void returnStock(String orderId, BigDecimal integral, List<OrderCartItem> cartInfoList,
            AfterSales afterSales) {
        YxStoreOrderQueryVo order = this.getOrderInfo(orderId, null);
        this.regressionIntegral(order, integral);
        this.regressionStock(order, cartInfoList, afterSales);
    }

    /**
     * 根据ID回退库存和积分，退款回调专用，并处理报损问题
     */
    public void returnStockAndReportLoss(String orderId, BigDecimal integral, List<OrderCartItem> cartInfoList,
            AfterSales afterSales) {
        YxStoreOrderQueryVo order = this.getOrderInfo(orderId, null);
        this.regressionIntegral(order, integral);
        this.regressionStockAndReportLoss(order, cartInfoList, afterSales);
    }

    /**
     * 管理后台，分页订单查询
     */
    @Override
    public PageResult<YxStoreOrderDto> queryPagedResult(YxStoreOrderQueryCriteria criteria, Pageable pageable) {
        getPageByStartPage0(pageable);

        PageInfo<Order> page = new PageInfo<>(queryAllOrder(criteria));

        List<YxStoreOrderDto> storeOrderDTOS = new ArrayList<>();
        Map<String, Boolean> refundStatusMap = new HashMap<>();
        for (Order yxStoreOrder : page.getList()) {
            this.appendOrder2List(storeOrderDTOS, yxStoreOrder);
            refundStatusMap.put(yxStoreOrder.getOrderId(),
                    yxStoreOrder.getRefundStatus() == OrderRefundStatusEnum.REFUND_STATUS_2_DONE.getValue());
        }

        // 如果是单个退款订单查询，或者查询售后、退款订单，则附加退款订单信息
        boolean shouldAppendAfterSalesInfo = false;
        Integer status = criteria.getStatus();
        if (status == null || status == OrderInfoEnum.STATUS_AFTER_SALES.getValue()) {
            shouldAppendAfterSalesInfo = true;
        }

        if (criteria.getOrderId() != null) {
            shouldAppendAfterSalesInfo = true;
        }

        if (shouldAppendAfterSalesInfo) {
            // 附加退款订单信息
            for (YxStoreOrderDto storeOrderDTO : storeOrderDTOS) {
                // 获取已退款成功的信息
                boolean isRefundSuccess = refundStatusMap.get(storeOrderDTO.getOrderId());

                if (!isRefundSuccess) {
                    continue;
                }

                List<AfterSalesVo> afterSales = afterSalesService.getCSInfoByOrderCodeAndUid(storeOrderDTO.getOrderId(),
                        storeOrderDTO.getUid());

                if (afterSales.isEmpty()) {
                    continue;
                }

                // 获取退款成功的售后信息
                AfterSalesVo afterSalesVo = afterSales.stream()
                        .filter(x -> x.getState() == AfterSalesStateEnum.STATUS_DONE_3.getValue())
                        .findFirst().orElse(null);

                if (afterSalesVo == null) {
                    continue;
                }

                // 1. 获取售后中的商品列表
                List<AfterSalesItem> storeAfterSalesItems = storeAfterSalesItemMapper
                        .selectList(Wrappers.<AfterSalesItem>lambdaQuery().eq(AfterSalesItem::getStoreAfterSalesId,
                                afterSalesVo.getId()));

                // 2. 获取商品列表对应的购物车信息
                List<YxStoreCartQueryVo> cartItemList = storeAfterSalesItems.stream()
                        .map(cart -> {
                            YxStoreCartQueryVo data = JSON.parseObject(cart.getCartInfo(),
                                    YxStoreCartQueryVo.class);
                            if (data != null) {
                                AfterSalesItem asItem = storeAfterSalesItems.stream()
                                        .filter(x -> com.leway.utils.StringUtils.equals(x.getProductAttrUnique(),
                                                data.getProductAttrUnique()))
                                        .findFirst().orElse(null);
                                if (asItem != null) {
                                    data.setRefundNum(asItem.getNum());
                                } else {
                                    data.setRefundNum(0);
                                }
                            }
                            return data;
                        })
                        .collect(Collectors.toList());
                afterSalesVo.setCartInfo(cartItemList);

                List<AfterSalesVo> succRefundedAfterSalesList = new ArrayList<>();
                succRefundedAfterSalesList.add(afterSalesVo);

                storeOrderDTO.setAfterSalesList(succRefundedAfterSalesList);
            }

        }

        PageResult<YxStoreOrderDto> r = new PageResult<YxStoreOrderDto>();
        r.setContent(storeOrderDTOS);
        r.setTotalElements(page.getTotal());
        return r;
    }

    /**
     * 管理后台，全量订单查询
     */
    @Override
    public List<Order> queryAllOrder(YxStoreOrderQueryCriteria criteria) {
        Long productId = criteria.getProductId();
        if (productId == null || productId == 0) {
            QueryWrapper<Order> wrapper = QueryHelpPlus.getPredicate(Order.class, criteria);
            // 财务查询
            if (criteria.getFinanceQuery() != null && criteria.getFinanceQuery()) {
                // 调整限制状态
                criteria.setOrderId(null);
                criteria.setStatus(null);
                criteria.setPaid(PayStsEnum.PAY_STATUS_1_PAID.getValue());
                criteria.setRefundStatus(null);
                criteria.setCancelStatus(OrderInfoEnum.CANCEL_STATUS_0_NORMAL.getValue());
                criteria.setDeliveryStatus(null);
                criteria.setWechatOrderStatus(null);

                wrapper = QueryHelpPlus.getPredicate(Order.class, criteria);
                // 已完成、或者已经退款
                wrapper = wrapper.and(w -> {
                    w.in("status", OrderInfoEnum.STATUS_AFTER_SALES.getValue()).eq("refund_status",
                            OrderRefundStatusEnum.REFUND_STATUS_2_DONE.getValue());

                    w.or();

                    w.in("status", OrderInfoEnum.STATUS_2_DONE.getValue());
                })
                        .eq("is_del", 0)
                        .eq("cancel_status", 0)
                        .eq("paid", 1);

            }
            return baseMapper.selectList(wrapper);
        }

        // join 查询
        // 只有涉及到product的查询才 distinct
        MPJLambdaWrapper<Order> queryWrapper = JoinWrappers.lambda(Order.class)
                .distinct()
                .selectAll(Order.class)
                .leftJoin(OrderCartItem.class, OrderCartItem::getOid, Order::getId)
                .eq(OrderCartItem::getProductId, productId);

        if (criteria.getOrderId() != null) {
            queryWrapper = queryWrapper.and(w -> {
                w.eq(Order::getOrderId, criteria.getOrderId());
            });
            criteria.setOrderId(null);
        }
        if (criteria.getProductId() != null) {
            queryWrapper = queryWrapper.and(w -> {
                w.eq(OrderCartItem::getProductId, criteria.getProductId());
            });
            criteria.setProductId(null);
        }

        if (criteria.getFinanceQuery() != null && criteria.getFinanceQuery()) {
            // 调整限制状态
            // 这种情况下不允许查询orderId
            criteria.setOrderId(null);
            criteria.setStatus(null);
            criteria.setStatus(null);
            criteria.setPaid(PayStsEnum.PAY_STATUS_1_PAID.getValue());
            criteria.setRefundStatus(null);
            criteria.setCancelStatus(OrderInfoEnum.CANCEL_STATUS_0_NORMAL.getValue());
            criteria.setDeliveryStatus(null);
            criteria.setWechatOrderStatus(null);

            // 已完成、或者已经退款
            queryWrapper = queryWrapper.and(w -> {
                w.in("status", OrderInfoEnum.STATUS_AFTER_SALES.getValue()).eq("refund_status",
                        OrderRefundStatusEnum.REFUND_STATUS_2_DONE.getValue());

                w.or();

                w.in("status", OrderInfoEnum.STATUS_2_DONE.getValue());
            })
                    .eq("is_del", 0)
                    .eq("cancel_status", 0)
                    .eq("paid", 1);
        }
        // 补充其他查询条件
        QueryHelpPlus.mashupQueryPredicate(Order.class, criteria, queryWrapper);

        return baseMapper.selectJoinList(Order.class, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Order resources) {
        Order yxStoreOrder = this.getById(resources.getId());
        Order yxStoreOrder1 = this.getOne(new LambdaQueryWrapper<Order>()
                .eq(Order::getUnique, resources.getUnique()));
        if (yxStoreOrder1 != null && !yxStoreOrder1.getId().equals(yxStoreOrder.getId())) {
            throw new EntityExistException(Order.class, "unique", resources.getUnique());
        }
        yxStoreOrder.copy(resources);
        this.saveOrUpdate(yxStoreOrder);
    }

    /**
     * 下载订单
     */
    @Override
    public void download(List<YxStoreOrderDto> allOrderList, HttpServletResponse response) throws IOException {
        // 0. 序列化配置
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(Date.class, new JsonDeserializer<Date>() {
            @Override
            public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
                    throws JsonParseException {
                return new Date(json.getAsJsonPrimitive().getAsLong());
            }
        });
        // 表2：商品维度的汇总表
        // 商品名称 商品型号 商品ID 商品单价 邮费合计(分摊) 总数 订单数 总价
        List<Map<String, Object>> mergeList = new ArrayList<>();

        // 表3：按照快递公司名称汇总 name->快递单号
        Map<String, Set<String>> deliveryMap = new HashMap<>();

        // 合单map oid->合单商品数据
        Map<String, ProductMergeData> mergeMap = new HashMap<>();

        // 按快递公司统计 快递单号-> 订单数
        Map<String, Integer> deliveryCombinedMap = new HashMap<>();

        // 按照京东、顺丰统计快递面单数量
        for (YxStoreOrderDto yxStoreOrder : allOrderList) {
            if (StringUtils.isEmpty(yxStoreOrder.getDeliveryId())) {
                continue;
            }

            Set<String> deliverySet = deliveryMap.get(yxStoreOrder.getDeliveryName());
            if (deliverySet == null) {
                deliverySet = new HashSet<>();
            }
            deliverySet.add(yxStoreOrder.getDeliveryId());
            deliveryMap.put(yxStoreOrder.getDeliveryName(), deliverySet);

            if (deliveryCombinedMap.containsKey(yxStoreOrder.getDeliveryId())) {
                deliveryCombinedMap.put(yxStoreOrder.getDeliveryId(),
                        deliveryCombinedMap.get(yxStoreOrder.getDeliveryId()) + 1);
            } else {
                deliveryCombinedMap.put(yxStoreOrder.getDeliveryId(), 1);
            }
        }

        // 调整顺序，为了便于查看，根据地址信息key alphabet排序
        Map<String, YxStoreOrderDto> judgeMap = new HashMap<>(); // 合单信息
        // 没有合单的在前面
        // 存在合单的在后面
        int mergeNum = 0;
        List<YxStoreOrderDto> orderList = allOrderList;

        /**
         * 非合单订单
         */
        List<YxStoreOrderDto> normalExpressList = new ArrayList<>();
        /**
         * 已合单订单
         */
        List<YxStoreOrderDto> mergeExpressList = new ArrayList<>();
        for (YxStoreOrderDto o : orderList) {
            String key = o.delivieryKey();
            if (judgeMap.containsKey(key)) {
                YxStoreOrderDto originOrder = judgeMap.get(key);
                // 有合单
                if (mergeExpressList.indexOf(originOrder) == -1) {
                    // 可能为多单合单
                    mergeExpressList.add(originOrder);
                }
                mergeExpressList.add(o);

                normalExpressList.remove(o);
                normalExpressList.remove(originOrder);
                // 多单和为1单，重复记数
                mergeNum += 1;
            } else {
                judgeMap.put(key, o);
                normalExpressList.add(o);
            }
        }

        log.info("普通发货订单数: {}, 计划面单数: {}", normalExpressList.size(), judgeMap.size() - mergeNum);
        log.info("合并发货定单数: {}, 计划合单数: {}", mergeExpressList.size(), mergeNum);
        log.info("总订单数: {}, 计划总面单数: {}", orderList.size(), judgeMap.size());

        normalExpressList.addAll(mergeExpressList);
        orderList = normalExpressList;

        List<Map<String, Object>> list = new ArrayList<>();
        for (YxStoreOrderDto yxStoreOrder : orderList) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("订单号", yxStoreOrder.getOrderId());
            String status = "未知";
            Integer orderStatus = yxStoreOrder.getStatus();
            Integer payStatus = yxStoreOrder.getPaid();
            Integer refundStatus = yxStoreOrder.getRefundStatus();
            Integer deliveryStatus = yxStoreOrder.getDeliveryStatus();
            // map.put("订单状态（-1 : 申请退款 -2 : 退货成功 0：待发货；1：待收货；2：已收货；3：待评价；-1：已退款）",
            // yxStoreOrder.getStatus());
            switch (orderStatus) {
                case 0:
                    if (payStatus == 1) {
                        status = "待确认地址";
                    } else {
                        status = "待付款";
                    }
                    break;
                case 1:
                    if (OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.equalsTo(deliveryStatus)) {
                        status = "待发货";
                    } else if (OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.equalsTo(deliveryStatus)) {
                        status = "已发货";
                    } else {
                    }
                    break;
                case 2:
                    status = "已完成";
                    break;
                case -1:
                    status = "退款售后";
                    break;
                default:
                    break;
            }

            if (OrderRefundStatusEnum.REFUND_STATUS_2_DONE.equalsTo(refundStatus)) {
                status = "已退款";
            }

            // TODO:
            // yxStoreOrder.getStatusName();

            map.put("订单状态", status);

            map.put("用户id", yxStoreOrder.getUid());

            map.put("收货人", yxStoreOrder.getRealName());
            map.put("收货电话", yxStoreOrder.getUserPhone());
            map.put("收货地址", yxStoreOrder.getUserAddress());

            List<StoreOrderCartInfoDto> cartInfoList = yxStoreOrder.getCartInfoList();
            List<String> skuList = new ArrayList<>();
            List<String> numList = new ArrayList<>();
            cartInfoList.stream()
                    .forEach(x -> {
                        // 1. 设置sku
                        Gson gson = gsonBuilder.create();
                        String json = gson.toJson(x.getCartInfoMap());
                        YxStoreCartQueryVo cart = gson.fromJson(json, YxStoreCartQueryVo.class);
                        ProductQueryVo product = cart.getProductInfo();
                        String sku = String.format("%s-%s", product.getStoreName(), product.getAttrInfo().getSku());
                        skuList.add(sku);

                        // 2. 设置数量
                        String num = String.format("%s", cart.getCartNum());
                        numList.add(num);

                        // 3. 汇总数据
                        ProductMergeData mergeData = mergeMap.getOrDefault(sku, new ProductMergeData());
                        int total = mergeData.getTotalNum() + cart.getCartNum();
                        mergeData.setTotalNum(total);
                        Set<YxStoreOrderDto> refOrders = mergeData.getRefOrders();
                        if (!refOrders.contains(yxStoreOrder)) {
                            refOrders.add(yxStoreOrder);
                        }
                        mergeData.setRefOrders(refOrders);
                        mergeData.setProduct(product);

                        // 4. 分摊邮费计算
                        BigDecimal orderTotalItemNum = BigDecimal.valueOf(yxStoreOrder.getTotalNum());
                        BigDecimal postage = yxStoreOrder.getTotalPostage()
                                .multiply(BigDecimal.valueOf(cart.getCartNum())).divide(orderTotalItemNum);
                        mergeData.setPostage(postage.add(mergeData.getPostage()));

                        mergeMap.put(sku, mergeData);
                    });

            map.put("商品信息", String.join(";", skuList));
            map.put("商品数量", String.join(";", numList));
            // map.put("购物车id", yxStoreOrder.getCartId());

            // map.put("快递公司编号", yxStoreOrder.getDeliverySn());
            map.put("快递名称", yxStoreOrder.getDeliveryName());
            // map.put("发货类型", yxStoreOrder.getDeliveryType());
            map.put("快递单号", yxStoreOrder.getDeliveryId());

            Integer num = deliveryCombinedMap.getOrDefault(yxStoreOrder.getDeliveryId(), 0);
            if (num <= 1 || StringUtils.isEmpty(yxStoreOrder.getDeliveryId())) {
                map.put("是否合单", "否");
                map.put("合单数量", "/");
            } else {
                map.put("是否合单", "是");
                map.put("合单数量", num);
            }

            // map.put("运费金额", yxStoreOrder.getFreightPrice());
            map.put("订单商品总数", yxStoreOrder.getTotalNum());

            map.put("订单总价", yxStoreOrder.getTotalPrice());

            map.put("支付状态", PayStsEnum.getDesc(yxStoreOrder.getPaid()));
            map.put("支付时间", yxStoreOrder.getPayTime());
            map.put("支付方式", yxStoreOrder.getPayType());

            map.put("发货时间", yxStoreOrder.getDeliveryTime());

            map.put("备注", yxStoreOrder.getMark());
            map.put("管理员备注", yxStoreOrder.getRemark());

            map.put("邮费", yxStoreOrder.getTotalPostage());
            map.put("实际支付金额", yxStoreOrder.getPayPrice());
            // map.put("运费金额", yxStoreOrder.getPayPostage());

            // map.put("抵扣金额", yxStoreOrder.getDeductionPrice());
            // map.put("优惠券id", yxStoreOrder.getCouponId());
            // map.put("优惠券金额", yxStoreOrder.getCouponPrice());

            // map.put("0 未退款 1 申请中 2 已退款", yxStoreOrder.getRefundStatus());
            map.put("退款状态", OrderRefundStatusEnum.getDesc(yxStoreOrder.getRefundStatus()));
            // map.put("退款图片", yxStoreOrder.getRefundReasonWapImg());
            map.put("退款用户说明", yxStoreOrder.getRefundReasonWapExplain());
            map.put("退款申请时间", yxStoreOrder.getRefundReasonTime());
            map.put("前台退款原因", yxStoreOrder.getRefundReasonWap());
            map.put("不退款的理由", yxStoreOrder.getRefundReason());
            map.put("退款金额", yxStoreOrder.getRefundPrice());
            // map.put("消费赚取积分", yxStoreOrder.getGainIntegral());
            // map.put("使用积分", yxStoreOrder.getUseIntegral());
            // map.put("给用户退了多少积分", yxStoreOrder.getBackIntegral());
            // map.put("唯一id(md5加密)类似id", yxStoreOrder.getUnique());
            // map.put("成本价", yxStoreOrder.getCost());
            // map.put("核销码", yxStoreOrder.getVerifyCode());
            // map.put("门店id", yxStoreOrder.getStoreId());
            // map.put("配送方式 1=快递 ，2=门店自提", yxStoreOrder.getShippingType());
            // map.put("支付渠道(0微信公众号1微信小程序)", yxStoreOrder.getIsChannel());
            // map.put(" isRemind", yxStoreOrder.getIsRemind());
            list.add(map);
        }

        // 汇总
        Set<String> keysSet = mergeMap.keySet();
        // 排序
        List<String> keys = new ArrayList<>(keysSet);
        keys.sort(String::compareTo);

        // 按顺序输出
        keys.forEach(k -> {
            ProductMergeData v = mergeMap.get(k);
            Map<String, Object> map = new LinkedHashMap<>();
            ProductQueryVo product = v.getProduct();
            SKU sku = product.getAttrInfo();
            map.put("商品ID", product.getId());
            map.put("商品名称", product.getStoreName());
            map.put("商品型号", product.getAttrInfo().getSku());
            map.put("商品单价", product.getPrice());
            map.put("商品件数", v.getTotalNum());
            map.put("订单数量", v.getRefOrders().size());
            map.put("商品总价", NumberUtil.mul(v.getTotalNum(), sku.getPrice()));
            // map.put("邮费合计(分摊后)", v.getPostage());
            map.put("代扣邮费合计", v.getPostage());
            mergeList.add(map);
        });

        // 3. 按照物流维度的汇总表
        List<Map<String, Object>> deliveryList = new ArrayList<>();
        keysSet = deliveryMap.keySet();
        keys = new ArrayList<>(keysSet);
        keys.sort(String::compareTo);

        // 一行信息
        Map<String, Object> row = new LinkedHashMap<>();
        keys.forEach(deliveryName -> {
            Set<String> deliverySet = deliveryMap.get(deliveryName);
            row.put(deliveryName, deliverySet.size());
        });
        deliveryList.add(row);

        FileUtil.downloadExcel(list, mergeList, deliveryList, response);
    }

    /**
     * 管理后台：获取订单详情
     *
     * @param orderId
     * @return
     */
    @Override
    public YxStoreOrderDto getOrderDetail(Long orderId) {
        Order yxStoreOrder = this.getById(orderId);
        if (ObjectUtil.isEmpty(yxStoreOrder)) {
            throw new BadRequestException("订单详情不存在");
        }
        YxStoreOrderDto orderDto = generator.convert(yxStoreOrder, YxStoreOrderDto.class);
        Integer _status = OrderUtil.getAdminManagerOrderStatus(yxStoreOrder.getPaid(), yxStoreOrder.getStatus(),
                yxStoreOrder.getRefundStatus(), yxStoreOrder.getWechatOrderStatus());

        // if (yxStoreOrder.getStoreId() > 0) {
        // String storeName =
        // systemStoreService.getById(yxStoreOrder.getStoreId()).getName();
        // orderDto.setStoreName(storeName);
        // }

        // 订单状态
        String orderStatusStr = OrderUtil.getAdminOrderStatusStr(yxStoreOrder.getPaid(), yxStoreOrder.getStatus(),
                yxStoreOrder.getShippingType(), yxStoreOrder.getRefundStatus(), yxStoreOrder.getDeliveryStatus(),
                yxStoreOrder.getCancelStatus());

        // 售后中
        if (_status == 3) {
            String refundTime = DateUtil.formatDateTime(yxStoreOrder.getRefundReasonTime());
            String str = "<b style='color:#f12424'>申请退款</b>" +
                    "<span>退款原因：" + yxStoreOrder.getRefundReasonWap() + "</span>" +
                    "<span>备注说明：" + yxStoreOrder.getRefundReasonWapExplain() + "</span>" +
                    "<span>退款时间：" + refundTime + "</span>";
            orderStatusStr = str;

            List<AfterSalesVo> afterSales = afterSalesService.getCSInfoByOrderCodeAndUid(yxStoreOrder.getOrderId(),
                    yxStoreOrder.getUid());
            orderDto.setAfterSalesList(afterSales);
            if (afterSales.size() > 0) {
                AfterSalesVo afterSale = afterSales.get(0);
                String desc = getAfterSalesDesc(yxStoreOrder, afterSale);
                orderStatusStr = desc;
            }
        }
        orderDto.setStatusName(orderStatusStr);

        orderDto.set_status(_status);

        String payTypeName = OrderUtil.payTypeName(yxStoreOrder.getPayType(), yxStoreOrder.getPaid());
        orderDto.setPayTypeName(payTypeName);

        // 订单类型处理

        // 添加订单状态
        List<OrderStatus> storeOrderStatuses = orderStatusService
                .list(new LambdaQueryWrapper<OrderStatus>()
                        .eq(OrderStatus::getOid, yxStoreOrder.getId()));
        List<YxStoreOrderStatusDto> orderStatusDtos = generator.convert(storeOrderStatuses,
                YxStoreOrderStatusDto.class);
        orderDto.setStoreOrderStatusList(orderStatusDtos);

        // 添加购物车详情
        List<OrderCartItem> cartInfos = orderCartInfoService.list(
                new LambdaQueryWrapper<OrderCartItem>().eq(OrderCartItem::getOid, yxStoreOrder.getId()));
        List<StoreOrderCartInfoDto> cartInfoDTOS = new ArrayList<>();
        for (OrderCartItem cartInfo : cartInfos) {
            StoreOrderCartInfoDto cartInfoDTO = new StoreOrderCartInfoDto();
            cartInfoDTO.setCartInfoMap(JSON.parseObject(cartInfo.getCartInfo()));

            cartInfoDTOS.add(cartInfoDTO);
        }
        orderDto.setCartInfoList(cartInfoDTOS);

        // 添加用户信息
        orderDto.setUserDTO(generator.convert(userService.getById(yxStoreOrder.getUid()), YxUserDto.class));
        if (orderDto.getUserDTO() == null) {
            orderDto.setUserDTO(new YxUserDto());
        }
        return orderDto;
    }

    @Override
    public List<YxStoreOrderDto> getByIds(List<String> ids) {
        List<Order> yxStoreOrders = this
                .list(new LambdaQueryWrapper<Order>().in(Order::getOrderId, ids));
        List<YxStoreOrderDto> storeOrderDTOS = new ArrayList<>();
        for (Order yxStoreOrder : yxStoreOrders) {
            // 向传入的订单列表增加订单
            this.appendOrder2List(storeOrderDTOS, yxStoreOrder);
        }
        return storeOrderDTOS;
    }

    private String getAfterSalesDesc(Order yxStoreOrder, AfterSalesVo afterSale) {
        String refundTime = DateUtil.formatDateTime(yxStoreOrder.getRefundReasonTime());
        String orderStatusStr = null;
        int saleState = afterSale.getSalesState();
        if (saleState == AfterSales.SALES_CANCEL_STATUS_MERCHANT_REJECT) {
            orderStatusStr = "售后状态：商家已拒绝";
        } else if (saleState == AfterSales.SALES_CANCEL_STATUS_USER_CANCEL) {
            orderStatusStr = "售后状态：用户已取消";
        } else if (saleState == AfterSales.SALES_CANCEL_STATUS_SYSTEM_CLOSE) {
            orderStatusStr = "售后状态：超时,已关闭";
        } else {
            int state = afterSale.getState();
            if (state == AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue()) {
                String str = "<b style='color:#f12424'>申请退款</b><br/>" +
                        "<span>退款原因：" + yxStoreOrder.getRefundReasonWap() + "</span><br/>" +
                        "<span>备注说明：" + yxStoreOrder.getRefundReasonWapExplain() + "</span><br/>" +
                        "<span>退款时间：" + refundTime + "</span><br/>";
                orderStatusStr = str;
            } else if (state == AfterSalesStateEnum.STATUS_REVIEW_OK_1.getValue()) {
                String str = "<b style='color:#24a024'>已同意退款，等待回寄</b><br/>" +
                        "<span>退款原因：" + yxStoreOrder.getRefundReasonWap() + "</span><br/>" +
                        "<span>备注说明：" + yxStoreOrder.getRefundReasonWapExplain() + "</span><br/>" +
                        "<span>退款时间：" + refundTime + "</span><br/>";
                orderStatusStr = str;
            } else if (state == AfterSalesStateEnum.STATUS_USER_SENT_OR_WAIT_REFUND_2.getValue()) {
                if (afterSale.getServiceType() == AfterSales.TYPE_BOTH) {
                    String str = "<b style='color:#24a024'>回寄中，等待商家确认</b><br/>" +
                            "<span>退款原因：" + yxStoreOrder.getRefundReasonWap() + "</span><br/>" +
                            "<span>备注说明：" + yxStoreOrder.getRefundReasonWapExplain() + "</span><br/>" +
                            "<span>退款时间：" + refundTime + "</span><br/>";
                    orderStatusStr = str;
                } else {
                    String str = "<b style='color:#24a024'>平台已审核，等待处理退款</b>";
                    orderStatusStr = str;
                }
            } else if (state == AfterSalesStateEnum.STATUS_DONE_3.getValue()) {
                orderStatusStr = "已完成退款";
            } else if (state == AfterSalesStateEnum.STATUS_DONE_WITH_ISSUE_4.getValue()) {
                orderStatusStr = "已完成，用户退回的商品无法二次销售";
            } else if (state == AfterSalesStateEnum.STATUS_DONE_WITH_EXCHANGE_5.getValue()) {
                orderStatusStr = "已完成，换货完成";
            } else {
                orderStatusStr = "未知状态";
            }
        }
        return orderStatusStr;
    }

    /**
     * 处理订单
     * 将订单加入订单列表中
     *
     * @param orderList        订单列表
     * @param wait2AddOrderObj 待加入订单对象
     */
    private void appendOrder2List(List<YxStoreOrderDto> orderList, Order wait2AddOrderObj) {
        YxStoreOrderDto orderDto = generator.convert(wait2AddOrderObj, YxStoreOrderDto.class);

        Integer _status = OrderUtil.getAdminManagerOrderStatus(wait2AddOrderObj.getPaid(), wait2AddOrderObj.getStatus(),
                wait2AddOrderObj.getRefundStatus(), wait2AddOrderObj.getWechatOrderStatus());

        // if (yxStoreOrder.getStoreId() > 0) {
        // YxSystemStore systemStore =
        // systemStoreService.getById(yxStoreOrder.getStoreId());
        // if (Objects.nonNull(systemStore)) {
        // String storeName = systemStore.getName();
        // orderDto.setStoreName(storeName);
        // }
        // }

        // 订单状态
        String orderStatusStr = OrderUtil.getAdminOrderStatusStr(wait2AddOrderObj.getPaid(),
                wait2AddOrderObj.getStatus(),
                wait2AddOrderObj.getShippingType(), wait2AddOrderObj.getRefundStatus(),
                wait2AddOrderObj.getDeliveryStatus(),
                wait2AddOrderObj.getCancelStatus());

        if (_status == 3) {

            String refundTime = DateUtil.formatDateTime(wait2AddOrderObj.getRefundReasonTime());

            List<AfterSalesVo> afterSales = afterSalesService.getCSInfoByOrderCodeAndUid(wait2AddOrderObj.getOrderId(),
                    wait2AddOrderObj.getUid());
            orderDto.setAfterSalesList(afterSales);
            if (afterSales.size() > 0) {
                AfterSalesVo afterSale = afterSales.get(0);
                String desc = getAfterSalesDesc(wait2AddOrderObj, afterSale);
                orderStatusStr = desc;
            }
        }
        orderDto.setStatusName(orderStatusStr);

        orderDto.set_status(_status);

        String payTypeName = OrderUtil.payTypeName(wait2AddOrderObj.getPayType(), wait2AddOrderObj.getPaid());
        orderDto.setPayTypeName(payTypeName);

        // 载入购物车数据
        List<OrderCartItem> cartInfos = orderCartInfoService.list(
                new LambdaQueryWrapper<OrderCartItem>().eq(OrderCartItem::getOid, wait2AddOrderObj.getId()));

        List<StoreOrderCartInfoDto> cartInfoDTOS = new ArrayList<>();
        for (OrderCartItem cartInfo : cartInfos) {
            StoreOrderCartInfoDto cartInfoDTO = new StoreOrderCartInfoDto();
            cartInfoDTO.setCartInfoMap(JSON.parseObject(cartInfo.getCartInfo()));

            cartInfoDTOS.add(cartInfoDTO);
        }
        orderDto.setCartInfoList(cartInfoDTOS);

        orderDto.setUserDTO(generator.convert(userService.getById(wait2AddOrderObj.getUid()), YxUserDto.class));
        if (orderDto.getUserDTO() == null) {
            orderDto.setUserDTO(new YxUserDto());
        }
        orderList.add(orderDto);
    }

    // 汇总临时库存
    @Transactional
    public void summarizeTmpStock(String date) {
        // 1. 获取并分类
        List<TmpInventory> tmpStocks = tmpInventoryService.getUnSyncedTmpStockByDate(date);
        Map<String, List<TmpInventory>> skuMap = new HashMap<>();
        tmpStocks.stream().forEach(x -> {
            List<TmpInventory> list = new ArrayList<>();
            if (skuMap.containsKey(x.getSkuId())) {
                list = skuMap.get(x.getSkuId());
            }

            list.add(x);
            skuMap.put(x.getSkuId(), list);
        });

        // 2. 标记已同步
        tmpInventoryService.markSyncedByDate(date);

        // 3. 按照sku创建汇总数据
        for (Map.Entry<String, List<TmpInventory>> entry : skuMap.entrySet()) {
            String skuId = entry.getKey();
            List<TmpInventory> list = entry.getValue();
            TmpInventory tmpI = list.stream().findFirst().orElse(null);
            Long spuId = tmpI.getSpuId();

            // 生成汇总数据
            int stockIn = list.stream().map(x -> x.getTotalStockIn()).mapToInt(Integer::intValue).sum();
            int stockOut = list.stream().map(x -> x.getTotalStockOut()).mapToInt(Integer::intValue).sum();

            tmpInventorySummaryService.insertTmpInventoryDailySummary(new TmpInventoryDailySummary() {
                {
                    setSpuId(spuId);
                    setSkuId(skuId);
                    setSummaryDate(date);
                    setTotalStockIn(stockIn);
                    setTotalStockOut(stockOut);
                    setTotalTransfers(0);
                    setInventoryAdjustments(stockIn - stockOut);
                    setSynced(false);
                }
            });
        }
    }

    /**
     * 根据日期同步所有SKU库存数据
     *
     * @param summaryDate yyyyMMdd
     */
    @Transactional
    @Override
    public void syncTmpInventory(String summaryDate) {
        // 0. 汇总tmp 到tmp summary
        summarizeTmpStock(summaryDate);

        // 1. 获取未同步的临时库存数据，不要去管历史了
        List<TmpInventoryDailySummary> tmpStockSummarys = tmpInventorySummaryService
                .getUnSyncedStockByDate(summaryDate);

        // 2. 设置入库、出库记录
        Map<Long, List<TmpInventoryDailySummary>> productMap = new HashMap();
        for (TmpInventoryDailySummary tmpInventoryDailySummary : tmpStockSummarys) {
            Long spuId = tmpInventoryDailySummary.getSpuId();
            List<TmpInventoryDailySummary> list = productMap.get(spuId);
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(tmpInventoryDailySummary);
            productMap.put(spuId, list);
        }

        for (Map.Entry<Long, List<TmpInventoryDailySummary>> entry : productMap.entrySet()) {
            Long spuId = entry.getKey();
            List<TmpInventoryDailySummary> tmpSum = entry.getValue();
            this._syncTmpInventoryBySpu(spuId, tmpSum);
        }
    }

    /**
     * 根据某一个日期同步所有SKU库存数据
     *
     * @param summaryDate yyyyMMdd
     */
    @Transactional
    @Override
    public void reSyncTmpInventory(String summaryDate) {
        log.info("重新同步临时库存汇总数据：{}", summaryDate);
        // 1. 获取未同步的临时库存数据
        List<TmpInventoryDailySummary> tmpStockSummarys = tmpInventorySummaryService
                .getUnSyncedStockByDate(summaryDate);

        log.info("{}未同步的sku临时库存汇总数据：{}个", summaryDate, tmpStockSummarys.size());

        // 2. 设置入库、出库记录
        Map<Long, List<TmpInventoryDailySummary>> productMap = new HashMap();
        for (TmpInventoryDailySummary tmpInventoryDailySummary : tmpStockSummarys) {
            Long spuId = tmpInventoryDailySummary.getSpuId();
            List<TmpInventoryDailySummary> list = productMap.getOrDefault(spuId, new ArrayList<>());
            list.add(tmpInventoryDailySummary);
            productMap.put(spuId, list);
        }

        log.info("待处理的商品数量: {}", productMap.size());
        for (Map.Entry<Long, List<TmpInventoryDailySummary>> entry : productMap.entrySet()) {
            Long spuId = entry.getKey();
            List<TmpInventoryDailySummary> skuDailySumList = entry.getValue();
            this._syncTmpInventoryBySpu(spuId, skuDailySumList);
        }
    }

    @Transactional
    void _syncTmpInventoryBySpu(Long spuId, List<TmpInventoryDailySummary> tmpSumList) {
        Product p = productService.getById(spuId);
        if (p == null) {
            log.error("同步临时库存失败：Couldn't find product: {}", spuId);
            return;
        }
        for (TmpInventoryDailySummary tmpInventoryDailySummary : tmpSumList) {
            this._syncTmpInventory(p, tmpInventoryDailySummary);
        }
    }

    @Transactional
    void _syncTmpInventory(Product p, TmpInventoryDailySummary tmpSum) {
        // 1. 出库记录
        int stockOut = tmpSum.getTotalStockOut();

        // 2. 入库记录
        int stockIn = tmpSum.getTotalStockIn();

        if (stockOut == 0 && stockIn == 0) {
            log.info("临时库存无变化，无需同步");
            return;
        }

        String skuUnique = tmpSum.getSkuId();

        List<SKU> skuList = skuService
                .list(new LambdaQueryWrapper<SKU>().eq(SKU::getUnique, skuUnique));
        SKU sku = skuList.stream().findFirst().orElse(null);
        if (sku == null) {
            throw new YshopException("sku not found");
        }

        DateTime t = DateUtil.parse(tmpSum.getSummaryDate(), DatePattern.PURE_DATE_PATTERN);

        // 入库记录
        if (stockIn > 0) {
            InventoryRecord record = inventoryRecordService.findBySkuId(sku.getId());
            InventoryTransaction it = new InventoryTransaction();
            it.setOperatorId(0L);
            it.setInventoryId(record.getInventoryId());
            it.setCost(null);
            it.setBatchNumber("-");
            it.setTransactionType(InventoryTransaction.TRANSACTION_TYPE_STOCK_IN);
            it.setQuantity(stockIn);
            it.setTransactionDate(t.toJdkDate());
            String remarks = String.format("%s 根据订单销售系统自动入库", tmpSum.getSummaryDate());
            it.setRemarks(remarks);

            it = inventoryTransactionService.saveTransaction(it);

            // 5. 修改库存
            int newQuantity = record.getCurrentQuantity() + stockIn;
            record.setCurrentQuantity(newQuantity);
            record = inventoryRecordService.saveInventoryRecord(record);

        }

        // 出库记录
        if (stockOut > 0) {
            InventoryRecord record = inventoryRecordService.findBySkuId(sku.getId());
            InventoryTransaction it = new InventoryTransaction();
            it.setOperatorId(0L);
            it.setInventoryId(record.getInventoryId());
            it.setCost(null);
            it.setBatchNumber("-");
            it.setTransactionType(InventoryTransaction.TRANSACTION_TYPE_STOCK_OUT);
            it.setQuantity(-stockOut);
            it.setTransactionDate(t.toJdkDate());
            String remarks = String.format("%s 根据订单销售系统自动出库", tmpSum.getSummaryDate());
            it.setRemarks(remarks);

            it = inventoryTransactionService.saveTransaction(it);

            // 5. 修改库存
            int newQuantity = record.getCurrentQuantity() - stockOut;
            record.setCurrentQuantity(newQuantity);
            record = inventoryRecordService.saveInventoryRecord(record);
        }

        // 标记已同步
        tmpInventorySummaryService.markSynced(tmpSum.getId());

        log.info("{} {} {} 临时库存同步操作完成", p.getId(), p.getStoreName(), sku.getSku());
    }

    /*
     * 按照商品排序订单
     * 1. 商品总数量，1还是多个
     * 2. 商品种类，1还是多个
     * 3. 商品、sku名称
     */
    @Override
    public List<YxStoreOrderDto> reorderDtoByProduct(List<YxStoreOrderDto> source) {
        GsonBuilder gsonBuilder = new GsonBuilder();

        gsonBuilder.registerTypeAdapter(Date.class, new JsonDeserializer<Date>() {
            @Override
            public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
                    throws JsonParseException {
                return new Date(json.getAsJsonPrimitive().getAsLong());
            }
        });
        Comparator<YxStoreOrderDto> comparator = new Comparator<YxStoreOrderDto>() {
            @Override
            public int compare(YxStoreOrderDto o1, YxStoreOrderDto o2) {
                List<StoreOrderCartInfoDto> cartInfo1 = o1.getCartInfoList();
                List<StoreOrderCartInfoDto> cartInfo2 = o2.getCartInfoList();

                List<String> skuList1 = cartInfo1.stream()
                        .map(x -> {
                            Gson gson = gsonBuilder.create();
                            String json = gson.toJson(x.getCartInfoMap());
                            YxStoreCartQueryVo cart = gson.fromJson(json, YxStoreCartQueryVo.class);

                            return String.format("%d-%020d-%s", o1.getTotalNum(), cart.getProductId(),
                                    cart.getProductAttrUnique());
                        })
                        .collect(Collectors.toList());
                List<String> skuList2 = cartInfo2.stream()
                        .map(x -> {
                            Gson gson = gsonBuilder.create();
                            String json = gson.toJson(x.getCartInfoMap());
                            YxStoreCartQueryVo cart = gson.fromJson(json, YxStoreCartQueryVo.class);

                            return String.format("%d-%020d-%s", o2.getTotalNum(), cart.getProductId(),
                                    cart.getProductAttrUnique());
                        })
                        .collect(Collectors.toList());

                // 排序
                skuList1.sort(Comparator.naturalOrder());
                skuList2.sort(Comparator.naturalOrder());

                return String.join(",", skuList1).compareTo(String.join(",", skuList2));
            }
        };

        // 排序
        source.sort(comparator);

        // 单商品和多个商品分开，多个商品cart id 包含,
        List<YxStoreOrderDto> singleProductList = new ArrayList<>();
        List<YxStoreOrderDto> multiProductList = new ArrayList<>();
        for (YxStoreOrderDto order : source) {
            List<StoreOrderCartInfoDto> cartInfoList = order.getCartInfoList();
            if (cartInfoList.size() > 1) {
                multiProductList.add(order);
            } else {
                singleProductList.add(order);
            }
        }
        singleProductList.addAll(multiProductList);
        return singleProductList;
    }

    /*
     * 按照商品排序订单
     */
    // private List<Order> reorderByProduct(List<Order> source) {
    // List<YxStoreOrderQueryVo> target = generator.convert(source,
    // YxStoreOrderQueryVo.class);
    // // 混入购物车信息
    // target = target.stream().map(x ->
    // mashupOrderCart(x)).collect(Collectors.toList());
    // Comparator<YxStoreOrderQueryVo> comparator = new
    // Comparator<YxStoreOrderQueryVo>() {
    // @Override
    // public int compare(YxStoreOrderQueryVo o1, YxStoreOrderQueryVo o2) {
    // List<YxStoreCartQueryVo> cartInfo1 = o1.getCartInfo();
    // List<YxStoreCartQueryVo> cartInfo2 = o2.getCartInfo();

    // List<String> skuList1 = cartInfo1.stream()
    // .map(x -> String.format("%020d-%s", x.getProductId(),
    // x.getProductAttrUnique()))
    // .collect(Collectors.toList());
    // List<String> skuList2 = cartInfo2.stream()
    // .map(x -> String.format("%020d-%s", x.getProductId(),
    // x.getProductAttrUnique()))
    // .collect(Collectors.toList());

    // // 排序
    // skuList1.sort(Comparator.naturalOrder());
    // skuList2.sort(Comparator.naturalOrder());

    // return String.join(",", skuList1).compareTo(String.join(",", skuList2));
    // }
    // };

    // // 排序
    // target.sort(comparator);

    // // 单商品和多个商品分开，多个商品cart id 包含,
    // List<YxStoreOrderQueryVo> singleProductList = new ArrayList<>();
    // List<YxStoreOrderQueryVo> multiProductList = new ArrayList<>();
    // for (YxStoreOrderQueryVo order : target) {
    // List<YxStoreCartQueryVo> cartInfoList = order.getCartInfo();
    // if (cartInfoList.size() > 1) {
    // multiProductList.add(order);
    // } else {
    // singleProductList.add(order);
    // }
    // }
    // singleProductList.addAll(multiProductList);

    // List<Order> ret = generator.convert(singleProductList, Order.class);
    // return ret;
    // }

    private final int MaxMergeBatchCnt = 1000;

    /**
     * 批量发货且进行合单
     * 限速，20单每秒
     */
    @Override
    public ResultWithMsg batchMakeEOrder(int depotId) {
        return batchMakeEOrder(depotId, MaxMergeBatchCnt);
    }

    /**
     * 批量发货，生成电子面单
     */
    @Override
    public ResultWithMsg batchMakeEOrder(int depotId, int batchNum) {
        final int lockExpireTime = 60 * 10; // 锁的超时时间 10分钟

        // 尝试获得锁
        boolean isLocked = redisUtils.redisTemplate.opsForValue().setIfAbsent(ShopConstants.LOCK_KEY_BATCH_DELIVERY,
                ShopConstants.KeyLocked, lockExpireTime,
                TimeUnit.SECONDS);

        // 如果不能获得锁，则直接返回
        if (!isLocked) {
            log.info("batchMakeEOrder method is already running.");
            return new ResultWithMsg(false, "批量发货正在执行中，请稍后再试");
        }

        KDBirdAddressInfo senderAddress = new KDBirdAddressInfo(
                "幻觉贸易", "18333189126", "河北省", "石家庄市", "裕华区", "槐底街道槐建里小区");

        if (depotId == ShopConstants.DEPOT_SHIJIAZHUANG_JD) {
            return _batchJDMakeEOrder(depotId, senderAddress, batchNum);
        } else {
            return _batchMakeSFEOrder(depotId, senderAddress, batchNum);
        }
    }

    /**
     * 批量顺丰发货
     */
    public ResultWithMsg _batchMakeSFEOrder(int depotId, KDBirdAddressInfo senderAddress, int batchNum) {
        KDBirdExpressService kuaidiBirdExpressService = ExpressAutoConfiguration.expressService();
        // 预干预
        List<YxStoreOrderDto> limitOrderList = new ArrayList<>();
        Set<String> lockOrderIDSet = ConcurrentHashMap.newKeySet(); // 线程安全的 Set
        try {
            log.info("批量发货开始");
            YxStoreOrderQueryCriteria query = new YxStoreOrderQueryCriteria();
            /**
             * 出库中的状态 = 已发货（出库中）
             */
            query.setStatus(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
            query.setDeliveryStatus(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue());
            query.setCancelStatus(OrderInfoEnum.CANCEL_STATUS_0_NORMAL.getValue());
            query.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());
            // 没有标记为独立发货
            query.setIndependently(false);

            /**
             * 最大批次
             */
            int pageSize = batchNum;

            Sort sort = Sort.by(Sort.Direction.ASC, "id");
            Pageable pageQuery = new SortPage(0, pageSize, sort);
            PageResult<YxStoreOrderDto> orderPagedRet = queryPagedResult(query, pageQuery);
            List<YxStoreOrderDto> orderList = orderPagedRet.getContent();
            log.info("批量发货，查询待出库订单完成: 共{}单", orderList.size());

            // 重排序
            orderList = reorderDtoByProduct(orderList);
            log.info("批量发货，重排序完成：共{}单", orderList.size());

            // final int rateLimit = 20; // 每秒20个订单

            // 1. 第一次遍历计算合单的订单 key 为 地址
            // Map<String, List<YxStoreOrderDto>> orderMergeMap = new HashMap<>();
            Set<String> skipOrderIDSet = ConcurrentHashMap.newKeySet(); // 线程安全的 Set
            Map<String, Address> addrDeliveryKeyMap = new ConcurrentHashMap<>();

            // 转换为并行流进行处理
            String nullKey = "null-key";
            Map<String, List<YxStoreOrderDto>> orderMergeMap = orderList.parallelStream()
                    .filter(o -> {
                        // 过滤不需要的地址
                        String address = o.getUserAddress();
                        if (address.contains("西藏") || address.contains("香港特别行政区") || address.contains("澳门特别行政区")
                                || address.contains("台湾省")) {
                            skipOrderIDSet.add(o.getOrderId());
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.groupingByConcurrent(o -> {
                        // 生成分组的键
                        String address = o.getUserAddress();
                        Address addr = null;
                        String delivieryKey = o.delivieryKey();
                        if (addrDeliveryKeyMap.containsKey(delivieryKey)) {
                            // 已经查询过且正确的物流地址，不再次发起查询
                            return delivieryKey;
                        }
                        try {
                            addr = kuaidiBirdExpressService.parseAddress(address);
                            if (addr == null) {
                                log.info("地址解析失败，跳过批量发货 orderID: {} {}", o.getOrderId(), address);
                                skipOrderIDSet.add(o.getOrderId());
                                return nullKey;
                            }

                            // Address addressVo = AddressParser.simpleParse(address);
                            // if (addressVo == null) {
                            // log.info("simple地址解析失败，跳过批量发货 orderID: {} {}", o.getOrderId(), address);
                            // skipOrderIDSet.add(o.getOrderId());
                            // return nullKey;
                            // }
                            // if (StringUtils.isNotEqual(addressVo.getCity(), addr.getCity())) {
                            // log.warn("智能解析不准确: {} 不等于 {}, 原始地址信息: {}", addr.getCity(),
                            // addressVo.getCity(),
                            // address);
                            // log.warn("addressVo: {}", addressVo);
                            // skipOrderIDSet.add(o.getOrderId());
                            // return nullKey;
                            // }

                            KDBirdAddressInfo receiverAddress = new KDBirdAddressInfo(
                                    o.getRealName(), o.getUserPhone(), addr.getProvince(), addr.getCity(),
                                    addr.getDistrict(), addr.getDetail());

                            boolean reachable = kuaidiBirdExpressService.queryAddress(receiverAddress, senderAddress,
                                    KDBirdExpressService.SF_CODE);
                            try {
                                Thread.sleep(50l);
                            } catch (InterruptedException e) {
                                //
                            }
                            if (!reachable) {
                                log.info("地址不可达，跳过批量发货 orderID: {} {}", o.getOrderId(), address);
                                skipOrderIDSet.add(o.getOrderId());
                                return nullKey;
                            }
                            try {
                                // 临时锁定订单状态
                                String key = String.format(ShopConstants.LOCK_KEY_TMP_LOCK_ORDER_STATUS,
                                        o.getOrderId());
                                // 锁5分钟
                                redisUtils.set(key, 1, 5 * 60, TimeUnit.SECONDS);
                                lockOrderIDSet.add(o.getOrderId());
                            } catch (Exception e) {
                                log.error("订单{} 临时锁定失败", o.getOrderId());
                            }

                            addrDeliveryKeyMap.put(o.delivieryKey(), addr);
                            return o.delivieryKey();
                        } catch (YshopException e) {
                            log.info("地址解析异常，跳过批量发货 orderID: {} {} {}", o.getOrderId(), address, e.getMessage());
                            skipOrderIDSet.add(o.getOrderId());
                            return nullKey;
                        }
                    }));

            // 使用得到的结果
            // 注意：此处应该处理 orderMergeMap 中可能包含 null 键的情况
            orderMergeMap.remove(nullKey);

            log.info("批量发货，合并信息检查完毕: 共{}个地址", orderMergeMap.size());

            // 2. 根据合单信息进行下单
            Map<String, ExpressOrderResp> expressOrderMap = new HashMap<>(); // 合单信息

            Map<String, YxStoreOrderDto> judgeMap = new HashMap<>(); // 合单信息
            // 没有合单的在前面
            // 存在合单的在后面
            int mergeNum = 0;
            List<YxStoreOrderDto> normalExpressList = new ArrayList<>();
            List<YxStoreOrderDto> mergeExpressList = new ArrayList<>();
            for (YxStoreOrderDto o : orderList) {
                String key = o.delivieryKey();
                if (judgeMap.containsKey(key)) {
                    YxStoreOrderDto originOrder = judgeMap.get(key);
                    // 有合单
                    mergeExpressList.add(originOrder);
                    mergeExpressList.add(o);

                    normalExpressList.remove(o);
                    normalExpressList.remove(originOrder);
                    mergeNum += 1;
                } else {
                    judgeMap.put(key, o);
                    normalExpressList.add(o);
                }
            }

            log.info("计划:普通发货订单数: {}, 计划面单数: {}", normalExpressList.size(), orderMergeMap.size() - mergeNum);
            log.info("计划:合并发货定单数: {}, 计划面单数: {}", mergeExpressList.size(), mergeNum);
            log.info("计划:总订单数: {}, 计划总面单数: {}", orderList.size(), orderMergeMap.size());

            normalExpressList.addAll(mergeExpressList);
            orderList = normalExpressList;

            for (int i = 0; i < orderList.size(); i++) {
                YxStoreOrderDto o = orderList.get(i);
                String key = o.delivieryKey();

                if (skipOrderIDSet.contains(o.getOrderId())) {
                    continue;
                }

                if (expressOrderMap.containsKey(key)) {
                    // 1. 已有同地址的快递面单
                    ExpressOrderResp expressOrder = expressOrderMap.get(key);

                    String orderId = o.getOrderId();

                    try {
                        orderDelivery(orderId, expressOrder.getLogisticCode(),
                                ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);
                        log.warn("顺丰合单: {}, 面单：{}, {}", orderId, expressOrder.getLogisticCode(), key);
                    } catch (YshopException e) {
                        log.error("订单{} 面单: {}, 顺丰合单失败, {}", o.getOrderId(), expressOrder.getLogisticCode(),
                                e.getMessage());
                    }
                } else {
                    // 2. 新下快递面单
                    YxStoreOrderQueryVo ov = getOrderInfo(o.getOrderId(), null);
                    // 2.1 获取所有相关的商品列表
                    List<YxStoreOrderDto> orderSkuList = orderMergeMap.getOrDefault(key, new ArrayList());

                    List<OrderCartItem> cartInfos = null;
                    if (orderSkuList.size() > 1) {
                        // 合单情况，合并商品（购物车）
                        cartInfos = new ArrayList<>();
                        for (int j = 0; j < orderSkuList.size(); j++) {
                            YxStoreOrderDto dbOrder = orderSkuList.get(j);
                            LambdaQueryWrapper<OrderCartItem> wrapper = new LambdaQueryWrapper<>();
                            wrapper.eq(OrderCartItem::getOid, dbOrder.getId());
                            List<OrderCartItem> tmpCartInfos = orderCartInfoService.list(wrapper);
                            cartInfos.addAll(tmpCartInfos);
                        }
                    }

                    try {
                        ExpressOrderResp sfExpressOrder = makeSFEOrder(ov, cartInfos, addrDeliveryKeyMap.get(key));
                        expressOrderMap.put(key, sfExpressOrder);
                    } catch (Exception e) {
                        log.error("订单{} ({}/{}) 顺丰下单失败, {}", o.getOrderId(), i, orderList.size(), e.getMessage());
                    }
                }
                // 睡眠 50 ms
                // 防止触发京东的限流策略
                // 并发不超过20次/S
                // 目前调用速度 15次左右
                try {
                    Thread.sleep(66);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

            log.info("共生成 {} 个运单", expressOrderMap.size());

        } finally {
            // 无论如何最后都要释放锁
            redisUtils.redisTemplate.delete(ShopConstants.LOCK_KEY_BATCH_DELIVERY);
            log.info("批量发货完成");

            for (String orderId : lockOrderIDSet) {
                try {
                    // 临时锁定订单状态
                    String key = String.format(ShopConstants.LOCK_KEY_TMP_LOCK_ORDER_STATUS, orderId);
                    // 解锁
                    redisUtils.del(key);
                } catch (Exception e) {
                    log.error("解锁失败: orderId: {}", orderId);
                }
            }
        }
        return new ResultWithMsg(true, "OK", limitOrderList);
    }

    /**
     * 批量京东发货
     */
    public ResultWithMsg _batchJDMakeEOrder(int depotId, KDBirdAddressInfo senderAddress, int batchNum) {
        KDBirdExpressService kuaidiBirdExpressService = ExpressAutoConfiguration.expressService();
        // 预干预
        List<YxStoreOrderDto> limitOrderList = new ArrayList<>();
        Set<String> lockOrderIDSet = ConcurrentHashMap.newKeySet(); // 线程安全的 Set
        try {
            log.info("批量发货开始");
            YxStoreOrderQueryCriteria query = new YxStoreOrderQueryCriteria();
            /**
             * 出库中的状态 = 已发货（出库中）
             */
            query.setStatus(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
            query.setDeliveryStatus(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue());
            query.setCancelStatus(OrderInfoEnum.CANCEL_STATUS_0_NORMAL.getValue());
            query.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());
            // 没有标记为独立发货
            query.setIndependently(false);

            /**
             * 最大批次
             */
            int pageSize = batchNum;

            Sort sort = Sort.by(Sort.Direction.ASC, "id");
            Pageable pageQuery = new SortPage(0, pageSize, sort);
            PageResult<YxStoreOrderDto> orderPagedRet = queryPagedResult(query, pageQuery);
            List<YxStoreOrderDto> orderList = orderPagedRet.getContent();
            log.info("批量发货，查询待出库订单完成: 共{}单", orderList.size());

            // 重排序
            orderList = reorderDtoByProduct(orderList);
            log.info("批量发货，重排序完成：共{}单", orderList.size());

            // final int rateLimit = 20; // 每秒20个订单

            // 1. 第一次遍历计算合单的订单 key 为 地址
            // Map<String, List<YxStoreOrderDto>> orderMergeMap = new HashMap<>();
            Set<String> skipOrderIDSet = ConcurrentHashMap.newKeySet(); // 线程安全的 Set
            Map<String, Address> addrDeliveryKeyMap = new ConcurrentHashMap<>();

            // 转换为并行流进行处理
            String nullKey = "null-key";
            Map<String, List<YxStoreOrderDto>> orderMergeMap = orderList.parallelStream()
                    .filter(o -> {
                        // 过滤不需要的地址
                        String address = o.getUserAddress();
                        if (address.contains("香港特别行政区") || address.contains("澳门特别行政区") || address.contains("台湾省")) {
                            skipOrderIDSet.add(o.getOrderId());
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.groupingByConcurrent(o -> {
                        // 生成分组的键
                        String address = o.getUserAddress();
                        Address addr = null;
                        String delivieryKey = o.delivieryKey();
                        if (addrDeliveryKeyMap.containsKey(delivieryKey)) {
                            // 已经查询过且正确的物流地址，不再次发起查询
                            return delivieryKey;
                        }
                        try {
                            addr = kuaidiBirdExpressService.parseAddress(address);
                            if (addr == null) {
                                log.info("地址解析失败，跳过批量发货 orderID: {} {}", o.getOrderId(), address);
                                skipOrderIDSet.add(o.getOrderId());
                                return nullKey;
                            }

                            // Address addressVo = AddressParser.simpleParse(address);
                            // if (addressVo == null) {
                            // log.info("simple地址解析失败，跳过批量发货 orderID: {} {}", o.getOrderId(), address);
                            // skipOrderIDSet.add(o.getOrderId());
                            // return nullKey;
                            // }
                            // if (StringUtils.isNotEqual(addressVo.getCity(), addr.getCity())) {
                            // log.warn("智能解析不准确: {} 不等于 {}, 原始地址信息: {}", addr.getCity(),
                            // addressVo.getCity(),
                            // address);
                            // log.warn("addressVo: {}", addressVo);
                            // skipOrderIDSet.add(o.getOrderId());
                            // return nullKey;
                            // }

                            String safeDetail = Order.safeStrForExpress(addr.getDetail());
                            KDBirdAddressInfo receiverAddress = new KDBirdAddressInfo(
                                    o.getRealName(), o.getUserPhone(), addr.getProvince(), addr.getCity(),
                                    addr.getDistrict(), safeDetail);

                            boolean reachable = kuaidiBirdExpressService.queryAddress(receiverAddress, senderAddress,
                                    KDBirdExpressService.JD_CODE);
                            try {
                                Thread.sleep(50l);
                            } catch (InterruptedException e) {
                                //
                            }

                            if (!reachable) {
                                log.info("地址不可达，跳过批量发货 orderID: {} {}", o.getOrderId(), address);
                                skipOrderIDSet.add(o.getOrderId());
                                return nullKey;
                            }

                            try {
                                // 临时锁定订单状态
                                String key = String.format(ShopConstants.LOCK_KEY_TMP_LOCK_ORDER_STATUS,
                                        o.getOrderId());
                                // 锁5分钟
                                redisUtils.set(key, 1, 5 * 60, TimeUnit.SECONDS);
                                lockOrderIDSet.add(o.getOrderId());
                            } catch (Exception e) {
                                log.error("订单{} 临时锁定失败", o.getOrderId());
                            }

                            addrDeliveryKeyMap.put(o.delivieryKey(), addr);
                            return o.delivieryKey();
                        } catch (YshopException e) {
                            log.info("地址解析异常，跳过批量发货 orderID: {} {} {}", o.getOrderId(), address, e.getMessage());
                            skipOrderIDSet.add(o.getOrderId());
                            return nullKey;
                        }
                    }));

            // 使用得到的结果
            // 注意：此处应该处理 orderMergeMap 中可能包含 null 键的情况
            orderMergeMap.remove(nullKey);

            log.info("批量发货，合并信息检查完毕: 共{}个地址", orderMergeMap.size());

            // 2. 根据合单信息进行下单
            Map<String, ExpressOrderResp> expressOrderMap = new HashMap<>(); // 合单信息
            for (int i = 0; i < orderList.size(); i++) {
                YxStoreOrderDto o = orderList.get(i);
                String key = o.delivieryKey();

                if (skipOrderIDSet.contains(o.getOrderId())) {
                    continue;
                }

                if (expressOrderMap.containsKey(key)) {
                    // 1. 已有同地址的快递面单
                    ExpressOrderResp expressOrder = expressOrderMap.get(key);

                    String orderId = o.getOrderId();

                    try {
                        orderDelivery(orderId, expressOrder.getLogisticCode(),
                                ShipperCodeEnum.JD.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);
                        log.warn("京东合单: {}, 面单：{}, {}", orderId, expressOrder.getLogisticCode(), key);
                    } catch (YshopException e) {
                        log.error("订单{} 面单: {}, 京东合单失败, {}", o.getOrderId(), expressOrder.getLogisticCode(),
                                e.getMessage());
                    }
                } else {
                    // 2. 新下快递面单
                    YxStoreOrderQueryVo ov = getOrderInfo(o.getOrderId(), null);
                    // 2.1 获取所有相关的商品列表
                    List<YxStoreOrderDto> orderSkuList = orderMergeMap.getOrDefault(key, new ArrayList());

                    List<OrderCartItem> cartInfos = null;
                    if (orderSkuList.size() > 1) {
                        // 合单情况，合并商品（购物车）
                        cartInfos = new ArrayList<>();
                        for (int j = 0; j < orderSkuList.size(); j++) {
                            YxStoreOrderDto dbOrder = orderSkuList.get(j);
                            LambdaQueryWrapper<OrderCartItem> wrapper = new LambdaQueryWrapper<>();
                            wrapper.eq(OrderCartItem::getOid, dbOrder.getId());
                            List<OrderCartItem> tmpCartInfos = orderCartInfoService.list(wrapper);
                            cartInfos.addAll(tmpCartInfos);
                        }
                    }

                    try {
                        ExpressOrderResp jdOrder = makeJDEOrder(ov, cartInfos, addrDeliveryKeyMap.get(key));
                        expressOrderMap.put(key, jdOrder);
                    } catch (JDLimitException e) {
                        // 京东预干预
                        limitOrderList.add(o);
                        log.error("订单{} ({}/{}) 京东下单需要预干预, {}", o.getOrderId(), i, orderList.size(), e.getMessage());
                    } catch (Exception e) {
                        log.error("订单{} ({}/{}) 京东下单失败, {}", o.getOrderId(), i, orderList.size(), e.getMessage());
                    }
                }
                // 睡眠 50 ms
                // 防止触发京东的限流策略
                // 并发不超过20次/S
                // 目前调用速度 15次左右
                try {
                    Thread.sleep(66);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

            log.info("共生成 {} 个运单", expressOrderMap.size());
            if (limitOrderList.size() > 0) {
                log.info("京东预干预订单总量: {}", limitOrderList.size());
                // 预干预订单
                for (YxStoreOrderDto o : limitOrderList) {
                    log.error("预干预订单: {}", o.getOrderId());
                }
            }

        } finally {

            // 无论如何最后都要释放锁
            redisUtils.redisTemplate.delete(ShopConstants.LOCK_KEY_BATCH_DELIVERY);
            log.info("批量发货完成");

            for (String orderId : lockOrderIDSet) {
                try {
                    // 临时锁定订单状态
                    String key = String.format(ShopConstants.LOCK_KEY_TMP_LOCK_ORDER_STATUS, orderId);
                    // 解锁
                    redisUtils.del(key);
                } catch (Exception e) {
                    log.error("解锁失败: orderId: {}", orderId);
                }
            }
        }
        return new ResultWithMsg(true, "OK", limitOrderList);
    }

    /**
     * 查询可能的问题单
     */
    // @Override
    // public void query0506UidDupOrder() {
    // // #region ids
    // final List<String> rows = Arrays.asList(
    // "1787378702980521984,JDVA26776463234",
    // "1787378648106442752,JDVA26776455547",
    // "1787378607996317696,JDVA26776444145",
    // "1787378570830585856,JDVA26776492312",
    // "1787378570813812736,JDVA26776455663",
    // "1787378569446469632,JDVA26776487783",
    // "1787378568515334144,JDVA26776484391",
    // "1787378567357702144,JDVA26776487882",
    // "1787378567248650240,JDVA26776468578",
    // "1787378566086828032,JDVA26776444316",
    // "1787378565780647937,JDVA26776468650",
    // "1787378564354580480,JDVA26776487946",
    // "1787378563993874432,JDVA26776444396",
    // "1787378563171790848,JDVA26776484609",
    // "1787378562832052224,JDVA26776484643",
    // "1787378562471337984,JDVA26776474231",
    // "1787378562311958528,JDVA26776488090",
    // "1787378562068688896,JDVA26776474357",
    // "1787378561619898368,JDVA26776492977",
    // "1787378561322098688,JDVA26776493015",
    // "1787378560923639808,JDVA26776469190",
    // "1787378560802009088,JDVA26776469253",
    // "1787378560776839168,JDVA26776474491",
    // "1787378559174615040,JDVA26776465321",
    // "1787378559128481792,JDVA26776484967",
    // "1787378558943928320,JDVA26776488558",
    // "1787378558511919104,JDVA26776495030",
    // "1787378557983432704,JDVA26776493446",
    // "1787378556322492416,JDVA26776476893",
    // "1787378555349409792,JDVA26776485192",
    // "1787378555223580672,JDVA26776469667",
    // "1787378554925785088,JDVA26776465563",
    // "1787378554892230656,JDVA26776493580",
    // "1787378554426667008,JDVA26776476957",
    // "1787378554204364801,JDVA26776445216",
    // "1787378554187587584,JDVA26776477012",
    // "1787378552941879296,JDVA26776496506",
    // "1787378552795082752,JDVA26776496541",
    // "1787378552648282113,JDVA26776469928",
    // "1787378552476315648,JDVA26776493796",
    // "1787378551566147584,JDVA26776465752",
    // "1787378550890868736,JDVA26776470011",
    // "1787378547954851840,JDVA26776488998",
    // "1787378547187298304,JDVA26776485625",
    // "1787378547015331840,JDVA26776465915",
    // "1787378546361016320,JDVA26776477319",
    // "1787378544867844096,JDVA26776509475",
    // "1787378544544886784,JDVA26776477435",
    // "1787378544494555136,JDVA26776477488",
    // "1787378542774890496,JDVA26776477525",
    // "1787378542284156928,JDVA26776489242",
    // "1787378541881499648,JDVA26776502107",
    // "1787378541826977792,JDVA26776489315",
    // "1787378541776646144,JDVA26776475527",
    // "1787378541336240128,JDVA26776470524",
    // "1787378540652572672,JDVA26776486075",
    // "1787378540384133120,JDVA26776479415",
    // "1787378538882572288,JDVA26776496640",
    // "1787378538744164352,JDVA26776489818",
    // "1787378538505089024,JDVA26776497037",
    // "1787378538060492800,JDVA26776537986",
    // "1787378537989189632,JDVA26776538193",
    // "1787378537779474432,JDVA26776490467",
    // "1787378537766891520,JDVA26776511403",
    // "1787378536919642112,JDVA26776538661",
    // "1787378536651206656,JDVA26776511789",
    // "1787378536605069312,JDVA26776511950",
    // "1787378536240164865,JDVA26776578576",
    // "1787378536227577856,JDVA26776498638",
    // "1787378535892033536,JDVA26776506416",
    // "1787378534986067968,JDVA26776499403",
    // "1787378534663102464,JDVA26776535729",
    // "1787378534415642624,JDVA26776539930",
    // "1787378534226894848,JDVA26776499798",
    // "1787378534201733120,JDVA26776516756",
    // "1787378533845217280,JDVA26776524389",
    // "1787378533169934336,JDVA26776518250",
    // "1787378531215388672,JDVA26776536663",
    // "1787378531060199424,JDVA26776540265",
    // "1787378529512497152,JDVA26776530500",
    // "1787378529390862336,JDVA26776537276",
    // "1787378529332142080,JDVA26776540741",
    // "1787378527742500865,JDVA26776519349",
    // "1787378527742500864,JDVA26776507128",
    // "1787378526811365376,JDVA26776519896",
    // "1787378526740066304,JDVA26776531642",
    // "1787378525251084288,JDVA26776560802",
    // "1787378525087510528,JDVA26776560910",
    // "1787378524886183936,JDVA26776520358",
    // "1787378524613554176,JDVA26776520465",
    // "1787378524609359873,JDVA26776560622",
    // "1787378523053273088,JDVA26776532380",
    // "1787378521937588224,JDVA26776532435",
    // "1787378521748844544,JDVA26776507881",
    // "1787378521228750848,JDVA26776507890",
    // "1787378520670904320,JDVA26776533792",
    // "1787378520440217600,JDVA26776520744",
    // "1787378519798489088,JDVA26776533873",
    // "1787378519773327360,JDVA26776533891",
    // "1787378519735578624,JDVA26776599231",
    // "1787378519697825792,JDVA26776534010",
    // "1787378518062051328,JDVA26776561649",
    // "1787378517973970944,JDVA26776551181",
    // "1787378517919440896,JDVA26776520977",
    // "1787378517898469376,JDVA26776508161",
    // "1787378517873307648,JDVA26776542524",
    // "1787378517118328832,JDVA26776542586",
    // "1787378516484993024,JDVA26776561953",
    // "1787378516006838272,JDVA26776542693",
    // "1787378513293123584,JDVA26776508395",
    // "1787378513066631168,JDVA26776562170",
    // "1787378513024688128,JDVA26776508493",
    // "1787378512794005504,JDVA26776555709",
    // "1787378512550735872,JDVA26776599959",
    // "1787378512349409280,JDVA26776521382",
    // "1787378512026443776,JDVA26776562296",
    // "1787378511879647232,JDVA26776521401",
    // "1787378510659100672,JDVA26776521464",
    // "1787378509849600000,JDVA26776551783",
    // "1787378508905881600,JDVA26776555851",
    // "1787378507916025856,JDVA26776508710",
    // "1787378507786002432,JDVA26776551856",
    // "1787378505986646016,JDVA26776571665",
    // "1787378505781125120,JDVA26776508790",
    // "1787378503868522496,JDVA26776600250",
    // "1787378503331655680,JDVA26776525927",
    // "1787378503059021824,JDVA26776525962",
    // "1787378502840922112,JDVA26776600359",
    // "1787378502543126528,JDVA26776556140",
    // "1787378502048194560,JDVA26776526161",
    // "1787378501221920768,JDVA26776600449",
    // "1787378501188362240,JDVA26776521922",
    // "1787378500013961216,JDVA26776562898",
    // "1787378499187683328,JDVA26776522031",
    // "1787378496377495552,JDVA26776526332",
    // "1787378496352333824,JDVA26776526377",
    // "1787378496071315456,JDVA26776556429",
    // "1787378495886766080,JDVA26776563116",
    // "1787378495769321472,JDVA26776572340",
    // "1787378495618330624,JDVA26776552604",
    // "1787378495555411968,JDVA26776542882",
    // "1787378495257620480,JDVA26776545475",
    // "1787378493324046336,JDVA26776545511",
    // "1787378492627787776,JDVA26776552881",
    // "1787378490811658240,JDVA26776545573",
    // "1787378490690023424,JDVA26776552945",
    // "1787378490333507584,JDVA26776545591",
    // "1787378489318481920,JDVA26776572753",
    // "1787378489045852160,JDVA26776545681",
    // "1787378488936804352,JDVA26776557005",
    // "1787378487888224256,JDVA26776527116",
    // "1787378487615594496,JDVA26776545762",
    // "1787378487175196672,JDVA26776543199",
    // "1787378487166808064,JDVA26776601384",
    // "1787378487007420416,JDVA26776527224",
    // "1787378486554439680,JDVA26776527269",
    // "1787378485052874752,JDVA26776573008",
    // "1787378484696363008,JDVA26776601510",
    // "1787378484516007936,JDVA26776547841",
    // "1787378484516003840,JDVA26776547894",
    // "1787378483647787008,JDVA26776543477",
    // "1787378483203190784,JDVA26776557355",
    // "1787378483089940480,JDVA26776566067",
    // "1787378482951528448,JDVA26776557462",
    // "1787378480694996992,JDVA26776543702",
    // "1787378480678219776,JDVA26776557535",
    // "1787378480439140352,JDVA26776529302",
    // "1787378480367837184,JDVA26776548220",
    // "1787378480166514688,JDVA26776529391",
    // "1787378479155687424,JDVA26776576156",
    // "1787378479029858304,JDVA26776543936",
    // "1787378478702702592,JDVA26776546447",
    // "1787378478568480768,JDVA26776557805",
    // "1787378478555897856,JDVA26776581826",
    // "1787378478534930432,JDVA26776576290",
    // "1787378478534926336,JDVA26776566471",
    // "1787378477876420608,JDVA26776546563",
    // "1787378477754785792,JDVA26776581907",
    // "1787378477381492736,JDVA26776544152",
    // "1787378476857204736,JDVA26776548696",
    // "1787378476827848704,JDVA26776546761",
    // "1787378476270006272,JDVA26776544306",
    // "1787378476207091712,JDVA26776576812",
    // "1787378475716358144,JDVA26776582259",
    // "1787378475389202432,JDVA26776546923",
    // "1787378474416119808,JDVA26776546994",
    // "1787378473648566272,JDVA26776567102",
    // "1787378472243470336,JDVA26776582456",
    // "1787378471421390848,JDVA26776582572",
    // "1787378471375253504,JDVA26776582619",
    // "1787378471295557632,JDVA26776567470",
    // "1787378470611890176,JDVA26776577487",
    // "1787378470486056960,JDVA26776554987",
    // "1787378470347644928,JDVA26776582896",
    // "1787378470234398720,JDVA26776588306",
    // "1787378469752057856,JDVA26776547707",
    // "1787378469655588864,JDVA26776555150",
    // "1787378468766392320,JDVA26776550058",
    // "1787378468695089152,JDVA26776619670",
    // "1787378467541655552,JDVA26776568002",
    // "1787378466493079552,JDVA26776563520",
    // "1787378466388226048,JDVA26776568082",
    // "1787378466161729536,JDVA26776578081",
    // "1787378465415147520,JDVA26776602123",
    // "1787378465398370304,JDVA26776588799",
    // "1787378465134129152,JDVA26776591627",
    // "1787378464093937664,JDVA26776620095",
    // "1787378463506735104,JDVA26776583868",
    // "1787378462693044224,JDVA26776564042",
    // "1787378462261030912,JDVA26776620176",
    // "1787378462017757184,JDVA26776589052",
    // "1787378461501857792,JDVA26776604246",
    // "1787378460977573888,JDVA26776589151",
    // "1787378460918853632,JDVA26776564231",
    // "1787378459572482048,JDVA26776604444",
    // "1787378459434065920,JDVA26776584273",
    // "1787378459308236800,JDVA26776564285",
    // "1787378459039805440,JDVA26776564330",
    // "1787378458263855104,JDVA26776592221",
    // "1787378457332719616,JDVA26776604606",
    // "1787378456724549632,JDVA26776564439",
    // "1787378456695185408,JDVA26776592373",
    // "1787378456384811008,JDVA26776569782",
    // "1787378456225427456,JDVA26776592536",
    // "1787378456154124288,JDVA26776592571",
    // "1787378455789219840,JDVA26776620716",
    // "1787378455617249280,JDVA26776584715",
    // "1787378455231373312,JDVA26776604947",
    // "1787378454933577728,JDVA26776589808",
    // "1787378454753222657,JDVA26776605082",
    // "1787378454379933696,JDVA26776570119",
    // "1787378454132469760,JDVA26776592877",
    // "1787378453452992512,JDVA26776621068",
    // "1787378453201330176,JDVA26776621158",
    // "1787378452425383936,JDVA26776590116",
    // "1787378452194701312,JDVA26776585191",
    // "1787378452194697216,JDVA26776570406",
    // "1787378451804631040,JDVA26776585255",
    // "1787378451779465216,JDVA26776604021",
    // "1787378451498446848,JDVA26776585319",
    // "1787378450777022464,JDVA26776585345",
    // "1787378450575699968,JDVA26776565283",
    // "1787378449984299008,JDVA26776590449",
    // "1787378449980108800,JDVA26776640021",
    // "1787378449833304064,JDVA26776621561",
    // "1787378449682313216,JDVA26776565429",
    // "1787378449501958144,JDVA26776605811",
    // "1787378449464205312,JDVA26776585516",
    // "1787378448293994496,JDVA26776621679",
    // "1787378447992008704,JDVA26776570810",
    // "1787378447526440960,JDVA26776585623",
    // "1787378447329304576,JDVA26776570837",
    // "1787378447258005504,JDVA26776593625",
    // "1787378447069257728,JDVA26776565617",
    // "1787378446964400128,JDVA26776585722",
    // "1787378446612078592,JDVA26776593786",
    // "1787378446192648192,JDVA26776593831",
    // "1787378445848715264,JDVA26776590942",
    // "1787378445722886144,JDVA26776590987",
    // "1787378445324431360,JDVA26776622272",
    // "1787378445215375360,JDVA26776640461",
    // "1787378444967915520,JDVA26776596531",
    // "1787378444653338624,JDVA26776640516",
    // "1787378444368125952,JDVA26776596576",
    // "1787378444154216449,JDVA26776573546",
    // "1787378444154216448,JDVA26776609321",
    // "1787378444074524672,JDVA26776606351",
    // "1787378443411824640,JDVA26776573627",
    // "1787378443135004672,JDVA26776606432",
    // "1787378442866565120,JDVA26776593921",
    // "1787378442057068544,JDVA26776640731",
    // "1787378441788633088,JDVA26776610546",
    // "1787378441629245440,JDVA26776622641",
    // "1787378441239179264,JDVA26776573797",
    // "1787378441037848576,JDVA26776640875",
    // "1787378440735862785,JDVA26776594066",
    // "1787378440471621632,JDVA26776573896",
    // "1787378438579990528,JDVA26776573986",
    // "1787378437510443008,JDVA26776640991",
    // "1787378437036482560,JDVA26776594229",
    // "1787378436877099008,JDVA26776660846",
    // "1787378436772241408,JDVA26776594345",
    // "1787378436432506880,JDVA26776606936",
    // "1787378435975323648,JDVA26776611015",
    // "1787378435828527104,JDVA26776574320",
    // "1787378435446841344,JDVA26776607054",
    // "1787378433349689344,JDVA26776611123",
    // "1787378433051893760,JDVA26776597431",
    // "1787378504246009856,JDVA26776499798",
    // "1787378490765520896,JDVA26776445216",
    // "1787378484469870592,JDVA26776524389",
    // "1787378483203190785,JDVA26776477012",
    // "1787378481621938176,JDVA26776538661",
    // "1787378453289410560,JDVA26776476893",
    // "1787378450483421184,JDVA26776599959",
    // "1787378449174798336,JDVA26776488998",
    // "1787378447601938432,JDVA26776499798",
    // "1787378446112956416,JDVA26776477435",
    // "1787378444292628480,JDVA26776560802",
    // "1787378441188843520,JDVA26776488090",
    // "1787378440735862784,JDVA26776533873",
    // "1787378440068964352,JDVA26776545681",
    // "1787378438840037376,JDVA26776508790",
    // "1787378436877099009,JDVA26776511950",
    // "1787378434079502336,JDVA26776545762");
    // // #endregion

    // // 记录uid 在05-06 有多单
    // Map<Long, List<Order>> uidOrderMap = new HashMap<>();
    // for (String row : rows) {
    // String[] arr = row.split(",");
    // String orderId = arr[0];
    // String logisticCode = arr[1];

    // Order dbOrder = getByOrderId(orderId);
    // long uid = dbOrder.getUid();

    // List<Order> uidOrderList = uidOrderMap.getOrDefault(uid, new ArrayList<>());

    // if (uidOrderList.size() == 0) {
    // List<Order> uidOrderList1 = queryAllOrder(new YxStoreOrderQueryCriteria() {
    // {
    // setUid(uid);
    // setCreateTime(Arrays.asList("2024-05-06 00:00:00", "2024-05-06 17:00:00"));
    // setStatus(OrderInfoEnum.STATUS_2_DONE.getValue());
    // }
    // });
    // uidOrderList.addAll(uidOrderList1);
    // uidOrderList1 = queryAllOrder(new YxStoreOrderQueryCriteria() {
    // {
    // setUid(uid);
    // setCreateTime(Arrays.asList("2024-05-06 00:00:00", "2024-05-06 17:00:00"));
    // setStatus(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
    // }
    // });
    // uidOrderList.addAll(uidOrderList1);

    // List<Order> uidOrderList2 = queryAllOrder(new YxStoreOrderQueryCriteria() {
    // {
    // setUid(uid);
    // setDeliveryTime(Arrays.asList("2024-05-08 00:00:00", "2024-05-08 10:00:00"));
    // setStatus(OrderInfoEnum.STATUS_2_DONE.getValue());
    // }
    // });
    // uidOrderList.addAll(uidOrderList2);
    // uidOrderList2 = queryAllOrder(new YxStoreOrderQueryCriteria() {
    // {
    // setUid(uid);
    // setDeliveryTime(Arrays.asList("2024-05-08 00:00:00", "2024-05-08 10:00:00"));
    // setStatus(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
    // }
    // });
    // uidOrderList.addAll(uidOrderList2);

    // uidOrderMap.put(uid, uidOrderList);
    // }
    // }

    // // 输出 uidOrderMap
    // int count = 0;
    // for (Map.Entry<Long, List<Order>> entry : uidOrderMap.entrySet()) {
    // long uid = entry.getKey();
    // List<Order> orderList = entry.getValue();
    // if (orderList.size() > 1) {
    // // 多单的物流单号必须不一样
    // Set<String> logisticCodeSet = new HashSet<>();
    // for (Order o : orderList) {
    // logisticCodeSet.add(o.getDeliveryId());
    // }
    // if (logisticCodeSet.size() > 1) {
    // log.info("========");
    // log.info("uid: {} 有多单可能: {}", uid, orderList.size());
    // // 去重输出
    // orderList = orderList.stream().distinct().collect(Collectors.toList());
    // for (Order o : orderList) {
    // YxStoreOrderDto od = getOrderDetail(o.getId());
    // log.info("orderID: {} deliveryID: {} status: {}, ctime: {}, deliveryTime:
    // {}", o.getOrderId(),
    // o.getDeliveryId(),
    // od.getStatusName(), od.getCreateTime(), od.getDeliveryTime());
    // }
    // log.info("========");
    // count++;
    // }
    // }
    // }
    // log.info("总共有 {} 个uid有多单可能", count);
    // }

    /**
     * 修复0506订单问题
     */
    // @Override
    // public void fix0506BatchOrderIssue() {
    // // 三单有异常
    // final List<String> rows = Arrays.asList(
    // "1787378466388226048,JDVA26776568082",
    // "1787378465415147520,JDVA26776602123",
    // "1787378456225427456,JDVA26776592536");

    // // final List<String> rows = Arrays.asList(
    // // "1787378702980521984,JDVA26776463234",
    // // "1787378648106442752,JDVA26776455547",
    // // "1787378607996317696,JDVA26776444145",
    // // "1787378570830585856,JDVA26776492312",
    // // "1787378570813812736,JDVA26776455663",
    // // "1787378569446469632,JDVA26776487783",
    // // "1787378568515334144,JDVA26776484391",
    // // "1787378567357702144,JDVA26776487882",
    // // "1787378567248650240,JDVA26776468578",
    // // "1787378566086828032,JDVA26776444316",
    // // "1787378565780647937,JDVA26776468650",
    // // "1787378564354580480,JDVA26776487946",
    // // "1787378563993874432,JDVA26776444396",
    // // "1787378563171790848,JDVA26776484609",
    // // "1787378562832052224,JDVA26776484643",
    // // "1787378562471337984,JDVA26776474231",
    // // "1787378562311958528,JDVA26776488090",
    // // "1787378562068688896,JDVA26776474357",
    // // "1787378561619898368,JDVA26776492977",
    // // "1787378561322098688,JDVA26776493015",
    // // "1787378560923639808,JDVA26776469190",
    // // "1787378560802009088,JDVA26776469253",
    // // "1787378560776839168,JDVA26776474491",
    // // "1787378559174615040,JDVA26776465321",
    // // "1787378559128481792,JDVA26776484967",
    // // "1787378558943928320,JDVA26776488558",
    // // "1787378558511919104,JDVA26776495030",
    // // "1787378557983432704,JDVA26776493446",
    // // "1787378556322492416,JDVA26776476893",
    // // "1787378555349409792,JDVA26776485192",
    // // "1787378555223580672,JDVA26776469667",
    // // "1787378554925785088,JDVA26776465563",
    // // "1787378554892230656,JDVA26776493580",
    // // "1787378554426667008,JDVA26776476957",
    // // "1787378554204364801,JDVA26776445216",
    // // "1787378554187587584,JDVA26776477012",
    // // "1787378552941879296,JDVA26776496506",
    // // "1787378552795082752,JDVA26776496541",
    // // "1787378552648282113,JDVA26776469928",
    // // "1787378552476315648,JDVA26776493796",
    // // "1787378551566147584,JDVA26776465752",
    // // "1787378550890868736,JDVA26776470011",
    // // "1787378547954851840,JDVA26776488998",
    // // "1787378547187298304,JDVA26776485625",
    // // "1787378547015331840,JDVA26776465915",
    // // "1787378546361016320,JDVA26776477319",
    // // "1787378544867844096,JDVA26776509475",
    // // "1787378544544886784,JDVA26776477435",
    // // "1787378544494555136,JDVA26776477488",
    // // "1787378542774890496,JDVA26776477525",
    // // "1787378542284156928,JDVA26776489242",
    // // "1787378541881499648,JDVA26776502107",
    // // "1787378541826977792,JDVA26776489315",
    // // "1787378541776646144,JDVA26776475527",
    // // "1787378541336240128,JDVA26776470524",
    // // "1787378540652572672,JDVA26776486075",
    // // "1787378540384133120,JDVA26776479415",
    // // "1787378538882572288,JDVA26776496640",
    // // "1787378538744164352,JDVA26776489818",
    // // "1787378538505089024,JDVA26776497037",
    // // "1787378538060492800,JDVA26776537986",
    // // "1787378537989189632,JDVA26776538193",
    // // "1787378537779474432,JDVA26776490467",
    // // "1787378537766891520,JDVA26776511403",
    // // "1787378536919642112,JDVA26776538661",
    // // "1787378536651206656,JDVA26776511789",
    // // "1787378536605069312,JDVA26776511950",
    // // "1787378536240164865,JDVA26776578576",
    // // "1787378536227577856,JDVA26776498638",
    // // "1787378535892033536,JDVA26776506416",
    // // "1787378534986067968,JDVA26776499403",
    // // "1787378534663102464,JDVA26776535729",
    // // "1787378534415642624,JDVA26776539930",
    // // "1787378534226894848,JDVA26776499798",
    // // "1787378534201733120,JDVA26776516756",
    // // "1787378533845217280,JDVA26776524389",
    // // "1787378533169934336,JDVA26776518250",
    // // "1787378531215388672,JDVA26776536663",
    // // "1787378531060199424,JDVA26776540265",
    // // "1787378529512497152,JDVA26776530500",
    // // "1787378529390862336,JDVA26776537276",
    // // "1787378529332142080,JDVA26776540741",
    // // "1787378527742500865,JDVA26776519349",
    // // "1787378527742500864,JDVA26776507128",
    // // "1787378526811365376,JDVA26776519896",
    // // "1787378526740066304,JDVA26776531642",
    // // "1787378525251084288,JDVA26776560802",
    // // "1787378525087510528,JDVA26776560910",
    // // "1787378524886183936,JDVA26776520358",
    // // "1787378524613554176,JDVA26776520465",
    // // "1787378524609359873,JDVA26776560622",
    // // "1787378523053273088,JDVA26776532380",
    // // "1787378521937588224,JDVA26776532435",
    // // "1787378521748844544,JDVA26776507881",
    // // "1787378521228750848,JDVA26776507890",
    // // "1787378520670904320,JDVA26776533792",
    // // "1787378520440217600,JDVA26776520744",
    // // "1787378519798489088,JDVA26776533873",
    // // "1787378519773327360,JDVA26776533891",
    // // "1787378519735578624,JDVA26776599231",
    // // "1787378519697825792,JDVA26776534010",
    // // "1787378518062051328,JDVA26776561649",
    // // "1787378517973970944,JDVA26776551181",
    // // "1787378517919440896,JDVA26776520977",
    // // "1787378517898469376,JDVA26776508161",
    // // "1787378517873307648,JDVA26776542524",
    // // "1787378517118328832,JDVA26776542586",
    // // "1787378516484993024,JDVA26776561953",
    // // "1787378516006838272,JDVA26776542693",
    // // "1787378513293123584,JDVA26776508395",
    // // "1787378513066631168,JDVA26776562170",
    // // "1787378513024688128,JDVA26776508493",
    // // "1787378512794005504,JDVA26776555709",
    // // "1787378512550735872,JDVA26776599959",
    // // "1787378512349409280,JDVA26776521382",
    // // "1787378512026443776,JDVA26776562296",
    // // "1787378511879647232,JDVA26776521401",
    // // "1787378510659100672,JDVA26776521464",
    // // "1787378509849600000,JDVA26776551783",
    // // "1787378508905881600,JDVA26776555851",
    // // "1787378507916025856,JDVA26776508710",
    // // "1787378507786002432,JDVA26776551856",
    // // "1787378505986646016,JDVA26776571665",
    // // "1787378505781125120,JDVA26776508790",
    // // "1787378503868522496,JDVA26776600250",
    // // "1787378503331655680,JDVA26776525927",
    // // "1787378503059021824,JDVA26776525962",
    // // "1787378502840922112,JDVA26776600359",
    // // "1787378502543126528,JDVA26776556140",
    // // "1787378502048194560,JDVA26776526161",
    // // "1787378501221920768,JDVA26776600449",
    // // "1787378501188362240,JDVA26776521922",
    // // "1787378500013961216,JDVA26776562898",
    // // "1787378499187683328,JDVA26776522031",
    // // "1787378496377495552,JDVA26776526332",
    // // "1787378496352333824,JDVA26776526377",
    // // "1787378496071315456,JDVA26776556429",
    // // "1787378495886766080,JDVA26776563116",
    // // "1787378495769321472,JDVA26776572340",
    // // "1787378495618330624,JDVA26776552604",
    // // "1787378495555411968,JDVA26776542882",
    // // "1787378495257620480,JDVA26776545475",
    // // "1787378493324046336,JDVA26776545511",
    // // "1787378492627787776,JDVA26776552881",
    // // "1787378490811658240,JDVA26776545573",
    // // "1787378490690023424,JDVA26776552945",
    // // "1787378490333507584,JDVA26776545591",
    // // "1787378489318481920,JDVA26776572753",
    // // "1787378489045852160,JDVA26776545681",
    // // "1787378488936804352,JDVA26776557005",
    // // "1787378487888224256,JDVA26776527116",
    // // "1787378487615594496,JDVA26776545762",
    // // "1787378487175196672,JDVA26776543199",
    // // "1787378487166808064,JDVA26776601384",
    // // "1787378487007420416,JDVA26776527224",
    // // "1787378486554439680,JDVA26776527269",
    // // "1787378485052874752,JDVA26776573008",
    // // "1787378484696363008,JDVA26776601510",
    // // "1787378484516007936,JDVA26776547841",
    // // "1787378484516003840,JDVA26776547894",
    // // "1787378483647787008,JDVA26776543477",
    // // "1787378483203190784,JDVA26776557355",
    // // "1787378483089940480,JDVA26776566067",
    // // "1787378482951528448,JDVA26776557462",
    // // "1787378480694996992,JDVA26776543702",
    // // "1787378480678219776,JDVA26776557535",
    // // "1787378480439140352,JDVA26776529302",
    // // "1787378480367837184,JDVA26776548220",
    // // "1787378480166514688,JDVA26776529391",
    // // "1787378479155687424,JDVA26776576156",
    // // "1787378479029858304,JDVA26776543936",
    // // "1787378478702702592,JDVA26776546447",
    // // "1787378478568480768,JDVA26776557805",
    // // "1787378478555897856,JDVA26776581826",
    // // "1787378478534930432,JDVA26776576290",
    // // "1787378478534926336,JDVA26776566471",
    // // "1787378477876420608,JDVA26776546563",
    // // "1787378477754785792,JDVA26776581907",
    // // "1787378477381492736,JDVA26776544152",
    // // "1787378476857204736,JDVA26776548696",
    // // "1787378476827848704,JDVA26776546761",
    // // "1787378476270006272,JDVA26776544306",
    // // "1787378476207091712,JDVA26776576812",
    // // "1787378475716358144,JDVA26776582259",
    // // "1787378475389202432,JDVA26776546923",
    // // "1787378474416119808,JDVA26776546994",
    // // "1787378473648566272,JDVA26776567102",
    // // "1787378472243470336,JDVA26776582456",
    // // "1787378471421390848,JDVA26776582572",
    // // "1787378471375253504,JDVA26776582619",
    // // "1787378471295557632,JDVA26776567470",
    // // "1787378470611890176,JDVA26776577487",
    // // "1787378470486056960,JDVA26776554987",
    // // "1787378470347644928,JDVA26776582896",
    // // "1787378470234398720,JDVA26776588306",
    // // "1787378469752057856,JDVA26776547707",
    // // "1787378469655588864,JDVA26776555150",
    // // "1787378468766392320,JDVA26776550058",
    // // "1787378468695089152,JDVA26776619670",
    // // "1787378467541655552,JDVA26776568002",
    // // "1787378466493079552,JDVA26776563520",
    // // "1787378466388226048,JDVA26776568082",
    // // "1787378466161729536,JDVA26776578081",
    // // "1787378465415147520,JDVA26776602123",
    // // "1787378465398370304,JDVA26776588799",
    // // "1787378465134129152,JDVA26776591627",
    // // "1787378464093937664,JDVA26776620095",
    // // "1787378463506735104,JDVA26776583868",
    // // "1787378462693044224,JDVA26776564042",
    // // "1787378462261030912,JDVA26776620176",
    // // "1787378462017757184,JDVA26776589052",
    // // "1787378461501857792,JDVA26776604246",
    // // "1787378460977573888,JDVA26776589151",
    // // "1787378460918853632,JDVA26776564231",
    // // "1787378459572482048,JDVA26776604444",
    // // "1787378459434065920,JDVA26776584273",
    // // "1787378459308236800,JDVA26776564285",
    // // "1787378459039805440,JDVA26776564330",
    // // "1787378458263855104,JDVA26776592221",
    // // "1787378457332719616,JDVA26776604606",
    // // "1787378456724549632,JDVA26776564439",
    // // "1787378456695185408,JDVA26776592373",
    // // "1787378456384811008,JDVA26776569782",
    // // "1787378456225427456,JDVA26776592536",
    // // "1787378456154124288,JDVA26776592571",
    // // "1787378455789219840,JDVA26776620716",
    // // "1787378455617249280,JDVA26776584715",
    // // "1787378455231373312,JDVA26776604947",
    // // "1787378454933577728,JDVA26776589808",
    // // "1787378454753222657,JDVA26776605082",
    // // "1787378454379933696,JDVA26776570119",
    // // "1787378454132469760,JDVA26776592877",
    // // "1787378453452992512,JDVA26776621068",
    // // "1787378453201330176,JDVA26776621158",
    // // "1787378452425383936,JDVA26776590116",
    // // "1787378452194701312,JDVA26776585191",
    // // "1787378452194697216,JDVA26776570406",
    // // "1787378451804631040,JDVA26776585255",
    // // "1787378451779465216,JDVA26776604021",
    // // "1787378451498446848,JDVA26776585319",
    // // "1787378450777022464,JDVA26776585345",
    // // "1787378450575699968,JDVA26776565283",
    // // "1787378449984299008,JDVA26776590449",
    // // "1787378449980108800,JDVA26776640021",
    // // "1787378449833304064,JDVA26776621561",
    // // "1787378449682313216,JDVA26776565429",
    // // "1787378449501958144,JDVA26776605811",
    // // "1787378449464205312,JDVA26776585516",
    // // "1787378448293994496,JDVA26776621679",
    // // "1787378447992008704,JDVA26776570810",
    // // "1787378447526440960,JDVA26776585623",
    // // "1787378447329304576,JDVA26776570837",
    // // "1787378447258005504,JDVA26776593625",
    // // "1787378447069257728,JDVA26776565617",
    // // "1787378446964400128,JDVA26776585722",
    // // "1787378446612078592,JDVA26776593786",
    // // "1787378446192648192,JDVA26776593831",
    // // "1787378445848715264,JDVA26776590942",
    // // "1787378445722886144,JDVA26776590987",
    // // "1787378445324431360,JDVA26776622272",
    // // "1787378445215375360,JDVA26776640461",
    // // "1787378444967915520,JDVA26776596531",
    // // "1787378444653338624,JDVA26776640516",
    // // "1787378444368125952,JDVA26776596576",
    // // "1787378444154216449,JDVA26776573546",
    // // "1787378444154216448,JDVA26776609321",
    // // "1787378444074524672,JDVA26776606351",
    // // "1787378443411824640,JDVA26776573627",
    // // "1787378443135004672,JDVA26776606432",
    // // "1787378442866565120,JDVA26776593921",
    // // "1787378442057068544,JDVA26776640731",
    // // "1787378441788633088,JDVA26776610546",
    // // "1787378441629245440,JDVA26776622641",
    // // "1787378441239179264,JDVA26776573797",
    // // "1787378441037848576,JDVA26776640875",
    // // "1787378440735862785,JDVA26776594066",
    // // "1787378440471621632,JDVA26776573896",
    // // "1787378438579990528,JDVA26776573986",
    // // "1787378437510443008,JDVA26776640991",
    // // "1787378437036482560,JDVA26776594229",
    // // "1787378436877099008,JDVA26776660846",
    // // "1787378436772241408,JDVA26776594345",
    // // "1787378436432506880,JDVA26776606936",
    // // "1787378435975323648,JDVA26776611015",
    // // "1787378435828527104,JDVA26776574320",
    // // "1787378435446841344,JDVA26776607054",
    // // "1787378433349689344,JDVA26776611123",
    // // "1787378433051893760,JDVA26776597431",
    // // "1787378504246009856,JDVA26776499798",
    // // "1787378490765520896,JDVA26776445216",
    // // "1787378484469870592,JDVA26776524389",
    // // "1787378483203190785,JDVA26776477012",
    // // "1787378481621938176,JDVA26776538661",
    // // "1787378453289410560,JDVA26776476893",
    // // "1787378450483421184,JDVA26776599959",
    // // "1787378449174798336,JDVA26776488998",
    // // "1787378447601938432,JDVA26776499798",
    // // "1787378446112956416,JDVA26776477435",
    // // "1787378444292628480,JDVA26776560802",
    // // "1787378441188843520,JDVA26776488090",
    // // "1787378440735862784,JDVA26776533873",
    // // "1787378440068964352,JDVA26776545681",
    // // "1787378438840037376,JDVA26776508790",
    // // "1787378436877099009,JDVA26776511950",
    // // "1787378434079502336,JDVA26776545762");

    // log.info("修复开始");
    // for (String row : rows) {
    // String[] arr = row.split(",");
    // String orderId = arr[0];
    // String logisticCode = arr[1];

    // Order dbOrder = getByOrderId(orderId);
    // String dbLogisticCode = dbOrder.getDeliveryId();
    // int deliverySts = dbOrder.getDeliveryStatus();
    // try {
    // if (StrUtil.isNotBlank(dbLogisticCode)) {
    // log.info("订单: {}, 已有面单: {}, deliverySts: {}", orderId, dbLogisticCode,
    // deliverySts);
    // continue;
    // }
    // orderDelivery(orderId, logisticCode,
    // ShipperCodeEnum.JD.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);
    // log.info("修复订单: {}, 面单：{}, deliverySts: {}", orderId, logisticCode,
    // deliverySts);
    // } catch (YshopException e) {
    // log.error("订单修复失败: {} 面单: {}, deliverySts: {}, msg: {}", orderId,
    // logisticCode, deliverySts,
    // e.getMessage());
    // }
    // }
    // log.info("修复完成，共修复 {} 个订单", rows.size());
    // }

    @Override
    public String makeJDEOrder(String orderId) {
        YxStoreOrderQueryVo dbOrder = getOrderInfo(orderId, null);
        if (ObjectUtil.isNull(dbOrder)) {
            throw new YshopException("订单不存在");
        }
        ExpressOrderResp jdOrder = makeJDEOrder(dbOrder);
        return jdOrder.getPrintTemplate();
    }

    /**
     * 京东下单
     *
     * @param dbOrder
     * @return
     */
    public ExpressOrderResp makeJDEOrder(YxStoreOrderQueryVo dbOrder) throws YshopException {
        return makeJDEOrder(dbOrder, null, null);
    }

    /**
     * 京东下单，指定合单后的全部商品（合单用）
     */
    public ExpressOrderResp makeJDEOrder(YxStoreOrderQueryVo dbOrder, List<OrderCartItem> cartInfos, Address addr)
            throws YshopException {
        // 0. 下单数据准备
        KDBirdExpressService kuaidiBirdExpressService = ExpressAutoConfiguration.expressService();

        if (addr == null) {
            try {
                addr = kuaidiBirdExpressService.parseAddress(dbOrder.getUserAddress());
                if (addr == null) {
                    throw new YshopException("收货地址解析异常");
                }
                // Address addressVo = AddressParser.simpleParse(dbOrder.getUserAddress());
                // if (addressVo == null) {
                // log.error("addressVo is null, dbOrder.getUserAddress(): {}",
                // dbOrder.getUserAddress());
                // throw new YshopException("收货地址解析异常");
                // }
                // if (StringUtils.isNotEqual(addressVo.getCity(), addr.getCity())) {
                // log.warn("智能解析不准确: {} 不等于 {}, 原始地址信息: {}", addr.getCity(),
                // addressVo.getCity(),
                // dbOrder.getUserAddress());
                // log.warn("addressVo: {}", addressVo);
                // throw new YshopException("收货地址解析异常");
                // }
            } catch (YshopException e) {
                throw new YshopException("收货地址解析异常");
            }
        }

        if (cartInfos == null || cartInfos.size() == 0) {
            // 不传入，则为当前订单自身的购物信息
            LambdaQueryWrapper<OrderCartItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderCartItem::getOid, dbOrder.getId());
            cartInfos = orderCartInfoService.list(wrapper);
            if (cartInfos.size() == 0) {
                throw new YshopException("订单内容异常");
            }
        }

        String combinedName = OrderCartItem.getSkuCombinedNameWithNum(cartInfos);

        // 1. 京东下单
        String safeDetail = Order.safeStrForExpress(addr.getDetail());
        ExpressOrderResp jdOrder = kuaidiBirdExpressService.makeJDEOrder(dbOrder.getOrderId(), combinedName,
                KDBirdExpressService.DEFAULT_PACKAGE_NUM, 1, 1,
                new KDBirdAddressInfo(
                        "幻觉贸易", "18333189126", "河北省", "石家庄市", "裕华区", "槐底街道槐建里小区"),

                new KDBirdAddressInfo(
                        dbOrder.getRealName(), dbOrder.getUserPhone(), addr.getProvince(), addr.getCity(),
                        addr.getDistrict(), safeDetail),
                dbOrder.getInsurePrice());

        if (jdOrder == null) {
            String msg = String.format("京东下单失败，订单号：%s", dbOrder.getOrderId());
            throw new YshopException(msg);
        }

        // 2. 修改系统状态
        orderDelivery(dbOrder.getOrderId(), jdOrder.getLogisticCode(),
                ShipperCodeEnum.JD.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

        return jdOrder;
    }

    @Override
    public String makeSFEOrder(String orderId) {
        YxStoreOrderQueryVo dbOrder = getOrderInfo(orderId, null);
        if (ObjectUtil.isNull(dbOrder)) {
            throw new YshopException("订单不存在");
        }
        ExpressOrderResp sfOrder = makeSFEOrder(dbOrder);
        String tmpl = sfOrder.getPrintTemplate();
        return tmpl;
    }

    public ExpressOrderResp makeSFEOrder(YxStoreOrderQueryVo dbOrder) {
        return makeSFEOrder(dbOrder, null);
    }

    public ExpressOrderResp makeSFEOrder(YxStoreOrderQueryVo dbOrder, List<OrderCartItem> cartInfos) {
        return makeSFEOrder(dbOrder, cartInfos, null);
    }

    /**
     * 顺丰下单
     */
    public ExpressOrderResp makeSFEOrder(YxStoreOrderQueryVo dbOrder, List<OrderCartItem> cartInfos, Address addr) {
        // 顺丰下单
        KDBirdExpressService kuaidiBirdExpressService = ExpressAutoConfiguration.expressService();

        if (addr == null) {
            addr = kuaidiBirdExpressService.parseAddress(dbOrder.getUserAddress());
            if (addr == null) {
                throw new YshopException("收货地址解析异常");
            }
            // Address addressVo = AddressParser.simpleParse(dbOrder.getUserAddress());
            // if (addressVo == null) {
            // log.error("addressVo is null, dbOrder.getUserAddress(): {}",
            // dbOrder.getUserAddress());
            // throw new YshopException("收货地址解析异常");
            // }
            // if (StringUtils.isNotEqual(addressVo.getCity(), addr.getCity())) {
            // log.warn("智能解析不准确: {} 不等于 {}, 原始地址信息: {}", addr.getCity(),
            // addressVo.getCity(),
            // dbOrder.getUserAddress());
            // log.warn("addressVo: {}", addressVo);
            // throw new YshopException("收货地址解析异常");
            // }
        }

        // 1. 获取商品
        if (cartInfos == null || cartInfos.size() == 0) {
            LambdaQueryWrapper<OrderCartItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderCartItem::getOid, dbOrder.getId());
            cartInfos = orderCartInfoService.list(wrapper);
            if (cartInfos.size() == 0) {
                throw new YshopException("订单内容异常");
            }
        }

        String combinedName = OrderCartItem.getSkuCombinedNameWithNum(cartInfos);

        ExpressOrderResp sfOrder = kuaidiBirdExpressService.makeSFEOrder(dbOrder.getOrderId(), combinedName,
                KDBirdExpressService.DEFAULT_PACKAGE_NUM, 1, 1,
                // new KDBirdAddressInfo(
                // "客服六部", "13521557754", "北京市", "北京市", "顺义区", "天柱中路18号"),

                new KDBirdAddressInfo(
                        "幻觉贸易", "18333189126", "河北省", "石家庄市", "裕华区", "槐底街道槐建里小区"),
                new KDBirdAddressInfo(
                        dbOrder.getRealName(), dbOrder.getUserPhone(), addr.getProvince(), addr.getCity(),
                        addr.getDistrict(), addr.getDetail()),
                dbOrder.getInsurePrice());

        if (sfOrder == null) {
            String msg = String.format("顺丰下单失败，订单号：%s", dbOrder.getOrderId());
            throw new YshopException(msg);
        }

        orderDelivery(dbOrder.getOrderId(), sfOrder.getLogisticCode(),
                ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

        return sfOrder;
    }

    @Override
    public boolean cancelJDEOrder(String orderId) {
        YxStoreOrderQueryVo dbOrder = getOrderInfo(orderId, null);
        if (ObjectUtil.isNull(dbOrder)) {
            throw new YshopException("订单不存在");
        }
        return cancelJDEOrder(dbOrder);
    }

    public boolean cancelJDEOrder(YxStoreOrderQueryVo dbOrder) {
        // 1. 获取商品
        KDBirdExpressService kuaidiBirdExpressService = ExpressAutoConfiguration.expressService();

        // 2. 取消京东面单
        boolean succ = kuaidiBirdExpressService.cancelEOrder(new CancelEOrderReq() {
            {
                setShipperCode(KDBirdExpressService.JD_CODE);
                setOrderCode(dbOrder.getOrderId());
                setExpNo(dbOrder.getDeliveryId());
                setCustomerName(KDBirdExpressService.JD_CUSTOM_ACCOUNT);
            }
        });

        if (succ) {
            // ! 这里物流状态并不会退回到待发货
            dbOrder.setDeliveryId("");
            dbOrder.setDeliverySn("");
            dbOrder.setDeliveryName("");
            dbOrder.setDeliveryStatus(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue());
            Order toUpdateOrder = generator.convert(dbOrder, Order.class);
            updateById(toUpdateOrder);
        }
        return succ;
    }

    @Override
    public boolean cancelSFEOrder(String orderId) {
        throw new YshopException("未实现");
    }

    public boolean cancelSFEOrder(YxStoreOrderQueryVo dbOrder) {
        throw new YshopException("未实现");
    }

    /**
     * 处理微信退款回调
     * https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_7&index=8
     * !!!同样的通知可能会多次发送给商户系统。商户系统必须能够正确处理重复的通知。
     * 需要幂等性处理
     * 通知频率为15s/15s/30s/3m/10m/20m/30m/30m/30m/60m/3h/3h/3h/6h/6h - 总计 24h4m）
     *
     * @param outTradeNo        微信下单时商户定单号
     * @param curReqRefundPrice 当前请求的退款金额(元)
     * @param outRefundNo       退款单号
     */
    @Override
    public boolean handleWechatRefundCallback(String outTradeNo, BigDecimal curReqRefundPrice, String outRefundNo) {
        // 检查24小时内是否已处理过该退款单
        String refundDoneKey = String.format("refund_done:%s", outRefundNo);
        Boolean isDone = redisUtils.getBoolean(refundDoneKey);
        if (isDone != null && isDone) {
            log.info("退款单号:{} 24小时内已处理成功,跳过重复处理", outRefundNo);
            return true;
        }
        // 使用outRefundNo作为锁key,防止重复处理同一笔退款
        String lockKey = String.format("refund_callback:%s", outRefundNo);
        try {
            // 尝试获取锁,超时时间5秒
            boolean locked = redisUtils.set(lockKey, "1", 5, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("退款回调重复处理,outRefundNo:{}", outRefundNo);
                return false;
            }
            return _handleWechatRefundCallback(outTradeNo, curReqRefundPrice, outRefundNo);
        } finally {
            // 释放锁
            redisUtils.del(lockKey);
        }
    }

    /**
     * 处理微信退款回调
     * 
     * @param outTradeNo        微信下单时商户定单号
     * @param curReqRefundPrice 当前请求的退款金额(元)
     * @param outRefundNo       退款单号
     * @return
     */
    private boolean _handleWechatRefundCallback(String outTradeNo, BigDecimal curReqRefundPrice, String outRefundNo) {
        String refundDoneKey = String.format("refund_done:%s", outRefundNo);
        YxStoreOrderQueryVo orderInfo = getOrderInfo(outTradeNo, null);
        // 已经退款成功，不退回库存
        // !!!已微信支付退款状态为准
        if (OrderRefundStatusEnum.REFUND_STATUS_2_DONE.equalsTo(orderInfo.getWechatRefundStatus())) {
            log.warn("订单: {} 微信支付已经退款，不会重新回滚库存", orderInfo.getId());
            return false;
        }

        String cartId = orderInfo.getCartId();
        LambdaQueryWrapper<OrderCartItem> query = new LambdaQueryWrapper<>();
        query.in(OrderCartItem::getCartId, Arrays.asList(cartId.split(",")));
        List<OrderCartItem> cartInfoList = orderCartInfoService.list(query);

        String clientId = snowFlakeService.getMySnowFlake().nextIdStr();
        long startTime = System.currentTimeMillis();
        long startProcTime = System.currentTimeMillis();
        // int retries = 0;
        // 执行库存增减操作
        List<Long> lockedSkuIds = new ArrayList<>();
        // 按SKU ID升序锁定商品列表
        List<OrderCartItem> needRefundItems = new ArrayList<>();

        for (OrderCartItem cartInfo : cartInfoList) {
            needRefundItems.add(cartInfo);
        }
        List<Long> skuIds = needRefundItems.stream()
                .map(OrderCartItem::cartInfoVO)
                .map(YxStoreCartQueryVo::getProductInfo)
                .map(ProductQueryVo::getAttrInfo)
                .map(SKU::getId)
                .collect(Collectors.toList());
        skuIds.sort(Long::compareTo);

        try {
            // !!!顺序加锁
            for (Long skuId : skuIds) {
                String lockKey = String.format(ShopConstants.LOCK_STOCK_SKU, skuId);
                if (!redisLock.tryLockWithWait(lockKey, clientId)) {
                    String msg = String.format("_handleWechatRefundCallback 获取锁失败: %s，客户端id: %s, 超时时间: %d, order: %s",
                            lockKey, clientId,
                            System.currentTimeMillis() - startTime, orderInfo.getOrderId());
                    log.error(msg);
                    throw new YshopException("系统繁忙，请稍后再试");
                }
                lockedSkuIds.add(skuId);
                log.info("_handleWechatRefundCallback 锁定商品: {}, clientId:{}", lockKey, clientId);
            }
            startProcTime = System.currentTimeMillis();

            // 查询最新的订单对应售后单
            LambdaQueryWrapper<AfterSales> afterSaleQuery = new LambdaQueryWrapper<>();
            afterSaleQuery.eq(AfterSales::getOrderCode, orderInfo.getOrderId())
                    .orderByDesc(AfterSales::getId).last("limit 1");

            AfterSales afterSales = afterSalesService.getOne(afterSaleQuery);

            // 是否有商品报损，这个时候需要先回归库存，同时报损商品
            BigDecimal integral = BigDecimal.ZERO;
            boolean shouldReportLoss = false;
            if (Objects.nonNull(afterSales)) {
                if (afterSales.getLossStatus() != null && afterSales.getLossStatus() == 1) {
                    shouldReportLoss = true;
                }
                if (afterSales.getServiceType() == AfterSales.TYPE_ONLY_MONEY) {
                    // 仅退款情况下，用户尚未获得积分，用户在确认收货时获得积分
                } else {
                    // 获取回退的积分数，这是粗算，实际
                    // !! 每日有cronjob来计算准确积分
                    integral = afterSales.getRefundAmount();
                }
            }

            // 上一笔退款
            BigDecimal lastRefundPrice = orderInfo.getRefundPrice();
            if (lastRefundPrice == null) {
                lastRefundPrice = BigDecimal.ZERO;
            }
            // 判断是退10元’运费‘
            boolean isRefundDeliveryFee = false;
            if (curReqRefundPrice.compareTo(new BigDecimal("10")) == 0
                    && lastRefundPrice.compareTo(BigDecimal.ZERO) > 0) {
                isRefundDeliveryFee = true;
            }

            // 退运费模式，不影响其他
            // history: 历史上 1867303843226759168 退运费逻辑可能异常，引入的修复
            if (isRefundDeliveryFee) {
                BigDecimal willUpdateRefundPrice = lastRefundPrice.add(curReqRefundPrice);
                if (willUpdateRefundPrice.compareTo(orderInfo.getPayPrice()) > 0) {
                    log.error("退款金额超过实际支付金额，订单ID: {}, 退款金额: {}, 实际支付金额: {}", orderInfo.getId(), willUpdateRefundPrice,
                            orderInfo.getPayPrice());
                    return true;
                }

                Order updateForm = new Order();
                updateForm.setId(orderInfo.getId());
                // 退运费模式，更新退款总金额
                updateForm.setRefundPrice(willUpdateRefundPrice);
                updateById(updateForm);
                return true;
            }

            // 1. 回退库存和积分
            if (shouldReportLoss) {
                returnStockAndReportLoss(orderInfo.getOrderId(), integral, cartInfoList, afterSales);
            } else {
                returnStock(orderInfo.getOrderId(), integral, cartInfoList, afterSales);
            }

            // 3. 更新订单状态
            Order updateForm = new Order();
            updateForm.setId(orderInfo.getId());
            updateForm.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_2_DONE.getValue());

            // !!! 保留，但微信物流不会告诉这个信息
            updateForm.setWechatOrderStatus(WeiXinOrderShippingService.OrderState.REFUNDED.getCode());

            updateForm.setRefundPrice(curReqRefundPrice);
            // 微信支付退款时间
            updateForm.setFinishTime(new Date());

            updateById(updateForm);

            if (Objects.nonNull(afterSales)) {
                if (afterSales.getState() != AfterSalesStateEnum.STATUS_DONE_3.getValue()) {
                    afterSales.setState(AfterSalesStateEnum.STATUS_DONE_3.getValue());
                    afterSalesService.updateById(afterSales);
                }
            }

            // 处理成功,记录到Redis并设置24小时过期
            redisUtils.set(refundDoneKey, true, 24, TimeUnit.HOURS);
            return true;
        } catch (Exception e) {
            log.error("微信退款回调处理失败, outTradeNo: {}, outRefundNo: {}, msg: {}", outTradeNo, outRefundNo, e.getMessage());
            return false;
        } finally {
            // 确保释放锁
            Collections.reverse(lockedSkuIds);

            for (Long skuId : lockedSkuIds) {
                String lockKey = String.format(ShopConstants.LOCK_STOCK_SKU, skuId);
                int errorCode = redisLock.releaseLock(lockKey, clientId);
                if (errorCode == 1) {
                    log.warn("_handleWechatRefundCallback 锁已经释放，无需重复操作: {}, clientId:{}, errorCode:{}", lockKey,
                            clientId, errorCode);
                } else if (errorCode != 0) {
                    log.error("_handleWechatRefundCallback 释放锁失败: {}, clientId:{}, errorCode:{}", lockKey, clientId,
                            errorCode);
                }
            }

            long endProcTime = System.currentTimeMillis();
            log.info("_handleWechatRefundCallback 锁定耗时: {}ms, 执行耗时: {} ms", (startProcTime - startTime),
                    (endProcTime - startProcTime));
        }
    }

    /**
     * 上传物流信息给微信
     *
     * @param orderID
     * @return
     */
    public boolean uploadDeliveryInfo2Wechat(String orderID) {
        try {
            // 0. 获取订单
            YxStoreOrderQueryVo order = getOrderInfo(orderID, null);

            /**
             * 快递公司CODE(编码)
             */
            String deliverySn = order.getDeliverySn();

            // 1. 获取订单商品信息
            // 获取订单中所有的商品
            List<OrderCartItem> cartItemList = orderCartInfoService.getByOrder(orderID);
            String productName = OrderCartItem.getCombinedName(cartItemList);

            // 2. 判断订单发货的物流公司
            String outTradeNo = order.getOutTradeNo();
            YxUserQueryVo userQueryVo = userService.getYxUserById(order.getUid());
            String openId = userQueryVo.getWxMiniProgramID();

            String trackingNo = order.getDeliveryId();

            boolean succ = false;
            if (StringUtils.isEqual(deliverySn, KDBirdExpressService.JD_CODE)) {
                succ = weiXinOrderShippingService.syncJDOrderDeliveryInfo(outTradeNo, openId, productName,
                        trackingNo);
                // } else if (StringUtils.isEqual(deliverySn, KDBirdExpressService.SF_CODE)) {
                // // 顺丰发货人就是北京顺丰
                // String consignorContact = KDBirdExpressService.SF_PHONE;
                // String receiverContact = order.getUserPhone();
                // succ = weiXinOrderShippingService.syncSFOrderDeliveryInfo(outTradeNo, openId,
                // openId,
                // trackingNo, consignorContact, receiverContact);
            } else {
                // 其他物流公司
                succ = weiXinOrderShippingService.syncOrderDeliveryInfo(outTradeNo, openId, productName,
                        trackingNo, deliverySn);
            }

            if (succ) {
                Order storeOrder = new Order();
                storeOrder.setId(order.getId());
                storeOrder.setWechatOrderStatus(WeiXinOrderShippingService.OrderState.SHIPPED.getCode());
                update(storeOrder);
                return true;
            } else {
                log.warn("微信订单发货信息同步失败, orderID: {}, 快递代号: {}", orderID, deliverySn);
                return true;
            }
        } catch (Exception e) {
            log.error("微信订单发货信息同步失败, orderID: {}, msg: {}", orderID, e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 从微信物流助手同步信息到后端
     */
    @Override
    public void checkWechatDeliveryInfo2me() {
        // 1. 查询前已确认收货的订单

        // 这里为运营人员视角的订单状态：非数据库中的订单状态
        // 已确认收货，但订单未完成
        YxStoreOrderQueryCriteria criteria = new YxStoreOrderQueryCriteria();
        // 已付款
        criteria.setPaid(PayStsEnum.PAY_STATUS_1_PAID.getValue());
        // 待收货
        criteria.setStatus(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
        // 真实的已发出状态，不是虚拟的出库状态
        criteria.setDeliveryStatus(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue());
        // 无退款售后
        criteria.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());
        // 微信已经发货，尚未确认收货
        criteria.setWechatOrderStatus(WeiXinOrderShippingService.OrderState.SHIPPED.getCode());

        // 1. 分页拉取
        // 这里最大10w 已经确定了处理的最大范围
        int page = 0;
        int limit = 1000;
        Pageable pageable = Pageable.ofSize(limit).withPage(page);

        PageResult<YxStoreOrderDto> ret = queryPagedResult(criteria, pageable);
        List<YxStoreOrderDto> orderList = ret.getContent();
        long total = ret.getTotalElements();
        log.info("检查已发货商品在微信物流管理中的状态，总订单数量:{}", total);
        orderList = orderList == null ? new ArrayList<>() : orderList;

        while (orderList.size() < total) {
            // 翻页
            page += 1;
            pageable = pageable.withPage(page);
            ret = queryPagedResult(criteria, pageable);
            List<YxStoreOrderDto> tmpOrderList = ret.getContent();
            orderList.addAll(tmpOrderList);
        }

        // 2. 根据收货确认时间，确定需要自动完成的订单
        for (YxStoreOrderDto order : orderList) {
            syncWechatInfo2Order(order);
        }
    }

    /**
     * 同步已完成订单到微信
     */
    @Override
    public void syncFinishOrder2Wechat() {
        // 1. 查询前已确认收货的订单

        // 这里为运营人员视角的订单状态：非数据库中的订单状态
        // 已确认收货，但订单未完成
        YxStoreOrderQueryCriteria criteria = new YxStoreOrderQueryCriteria();
        // 已付款
        criteria.setPaid(PayStsEnum.PAY_STATUS_1_PAID.getValue());
        // 已完成
        criteria.setStatus(OrderInfoEnum.STATUS_2_DONE.getValue());
        // 无退款售后
        criteria.setRefundStatus(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue());

        // 微信已经发货，尚未确认收货
        criteria.setWechatOrderStatus(WeiXinOrderShippingService.OrderState.SHIPPED.getCode());

        // 1. 分页拉取
        // 这里最大10w 已经确定了处理的最大范围
        int page = 0;
        int limit = 1000;
        Pageable pageable = Pageable.ofSize(limit).withPage(page);

        PageResult<YxStoreOrderDto> ret = queryPagedResult(criteria, pageable);
        List<YxStoreOrderDto> orderList = ret.getContent();
        long total = ret.getTotalElements();
        log.info("查询已完成订单，微信物流状态尚未更新，总订单数量:{}", total);
        orderList = orderList == null ? new ArrayList<>() : orderList;

        while (orderList.size() < total) {
            // 翻页
            page += 1;
            pageable = pageable.withPage(page);
            ret = queryPagedResult(criteria, pageable);
            List<YxStoreOrderDto> tmpOrderList = ret.getContent();
            orderList.addAll(tmpOrderList);
        }

        // 2. 根据收货确认时间，确定需要自动完成的订单
        for (YxStoreOrderDto order : orderList) {
            syncWechatInfo2Order(order);
        }
    }

    /**
     * 同步微信订单
     */
    private void syncWechatInfo2Order(YxStoreOrderDto o) {
        long orderID = o.getId();
        // 已支付时间为准
        Date confirmTime = o.getPayTime();
        // 订单没到自动确认期，不进行处理
        // 10天确认期
        int days = 10;
        // 支付时间需要小于10天
        Date t = DateUtils.getZeroDateBeforeDay(days);
        Long uid = o.getUid();
        if (confirmTime.before(t)) {
            // 3. 系统完成确认收货过程
            confirm(o, uid);
        } else {
            log.info("订单ID: {} OutTradeNo: {} 未到自动确认收货时间，跳过", orderID, o.getOutTradeNo());
            // String outTradeNo = o.getOutTradeNo();
            // JSONObject wechatOrder =
            // weiXinOrderShippingService.queryOrderShippingStatus(null, outTradeNo);
            // if (wechatOrder == null) {
            // log.error("微信订单状态查询异常，订单号: {}", outTradeNo);
            // return;
            // }

            // int orderState = wechatOrder.getIntValue("order_state"); // Get order status
            // as an integer
            // if (orderState ==
            // WeiXinOrderShippingService.OrderState.CONFIRMED_RECEIPT.getCode() ||
            // orderState ==
            // WeiXinOrderShippingService.OrderState.TRADE_COMPLETED.getCode()) {
            // confirm(o, uid);
            // }
        }
    }

    private void confirm(YxStoreOrderDto o, Long uid) {
        long orderID = o.getId();
        try {
            // 3.1 执行确认
            boolean succ = wechatTakeOrder(o.getOrderId(), uid);
            log.info("订单ID: {} OutTradeNo: {} 微信确认收货成功: {}", orderID, o.getOutTradeNo(), succ);

            // 3.2 增加一个订单变更状态信息
            if (succ) {
                orderStatusService.create(orderID,
                        OrderLogEnum.MARK_DONE.getValue(),
                        OrderLogEnum.MARK_DONE.getDesc());
            }
            log.info("订单ID: {} OutTradeNo: {} 自动确认收货成功", orderID, o.getOutTradeNo());
        } catch (Exception e) {
            log.error("订单ID: {} OutTradeNo: {} 自动确认收货失败", orderID, o.getOutTradeNo(), e.getMessage());
        }
    }
}
